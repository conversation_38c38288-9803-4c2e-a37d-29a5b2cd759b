<template>
  <div class="login-container">
    <div class="login-card">
      <h1 class="login-title">英语学习助手</h1>
      <p class="login-subtitle">登录开始您的英语学习之旅</p>
      
      <form @submit.prevent="handleSubmit" class="login-form">
        <div class="form-group">
          <label for="email">邮箱</label>
          <input
            id="email"
            v-model="formData.email"
            type="email"
            required
            placeholder="请输入您的邮箱"
            :disabled="authStore.isLoading"
          />
        </div>
        
        <div class="form-group">
          <label for="password">密码</label>
          <input
            id="password"
            v-model="formData.password"
            type="password"
            required
            placeholder="请输入您的密码"
            :disabled="authStore.isLoading"
          />
        </div>
        
        <div class="form-options">
          <label class="checkbox-label">
            <input
              v-model="formData.rememberMe"
              type="checkbox"
              :disabled="authStore.isLoading"
            />
            记住我
          </label>
        </div>
        
        <button
          type="submit"
          class="login-btn"
          :disabled="authStore.isLoading || !isFormValid"
        >
          {{ authStore.isLoading ? '登录中...' : '登录' }}
        </button>
        
        <div class="form-footer">
          <p>还没有账户？ 
            <button 
              type="button" 
              @click="switchToRegister" 
              class="link-btn"
              :disabled="authStore.isLoading"
            >
              立即注册
            </button>
          </p>
        </div>
      </form>
      
      <!-- 注册表单 -->
      <form v-if="isRegisterMode" @submit.prevent="handleRegister" class="login-form">
        <h2>注册新账户</h2>
        
        <div class="form-group">
          <label for="username">用户名</label>
          <input
            id="username"
            v-model="registerData.username"
            type="text"
            required
            placeholder="请输入用户名"
            :disabled="authStore.isLoading"
          />
        </div>
        
        <div class="form-group">
          <label for="reg-email">邮箱</label>
          <input
            id="reg-email"
            v-model="registerData.email"
            type="email"
            required
            placeholder="请输入您的邮箱"
            :disabled="authStore.isLoading"
          />
        </div>
        
        <div class="form-group">
          <label for="firstName">名字</label>
          <input
            id="firstName"
            v-model="registerData.firstName"
            type="text"
            placeholder="请输入您的名字"
            :disabled="authStore.isLoading"
          />
        </div>
        
        <div class="form-group">
          <label for="lastName">姓氏</label>
          <input
            id="lastName"
            v-model="registerData.lastName"
            type="text"
            placeholder="请输入您的姓氏"
            :disabled="authStore.isLoading"
          />
        </div>
        
        <div class="form-group">
          <label for="reg-password">密码</label>
          <input
            id="reg-password"
            v-model="registerData.password"
            type="password"
            required
            placeholder="请输入密码（至少6位）"
            :disabled="authStore.isLoading"
          />
        </div>
        
        <button
          type="submit"
          class="login-btn"
          :disabled="authStore.isLoading || !isRegisterFormValid"
        >
          {{ authStore.isLoading ? '注册中...' : '注册' }}
        </button>
        
        <div class="form-footer">
          <p>已有账户？ 
            <button 
              type="button" 
              @click="switchToLogin" 
              class="link-btn"
              :disabled="authStore.isLoading"
            >
              立即登录
            </button>
          </p>
        </div>
      </form>
      
      <!-- 错误提示 -->
      <div v-if="authStore.error" class="error-message">
        {{ authStore.error }}
        <button @click="authStore.clearError" class="close-btn">×</button>
      </div>
      
      <!-- 测试用户提示 -->
      <div class="test-info">
        <h3>测试账户</h3>
        <p>邮箱: <EMAIL></p>
        <p>密码: 123456</p>
        <button @click="fillTestData" class="test-btn">使用测试账户</button>
        <button @click="testConnection" class="test-btn" style="margin-left: 8px;">测试连接</button>
      </div>

      <!-- 连接状态 -->
      <div v-if="connectionStatus" class="connection-status" :class="connectionStatus.type">
        {{ connectionStatus.message }}
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { commonAPI, handleApiError } from '../services/api'

export default {
  name: 'LoginView',
  setup() {
    const router = useRouter()
    const authStore = useAuthStore()

    const isRegisterMode = ref(false)
    const connectionStatus = ref(null)
    
    // 登录表单数据
    const formData = ref({
      email: '',
      password: '',
      rememberMe: false
    })
    
    // 注册表单数据
    const registerData = ref({
      username: '',
      email: '',
      firstName: '',
      lastName: '',
      password: ''
    })
    
    // 表单验证
    const isFormValid = computed(() => {
      return formData.value.email && formData.value.password
    })
    
    const isRegisterFormValid = computed(() => {
      return registerData.value.username && 
             registerData.value.email && 
             registerData.value.password &&
             registerData.value.password.length >= 6
    })
    
    // 处理登录
    const handleSubmit = async () => {
      const result = await authStore.login(formData.value)
      
      if (result.success) {
        router.push('/')
      }
    }
    
    // 处理注册
    const handleRegister = async () => {
      const result = await authStore.register(registerData.value)
      
      if (result.success) {
        router.push('/')
      }
    }
    
    // 切换到注册模式
    const switchToRegister = () => {
      isRegisterMode.value = true
      authStore.clearError()
    }
    
    // 切换到登录模式
    const switchToLogin = () => {
      isRegisterMode.value = false
      authStore.clearError()
    }
    
    // 填充测试数据
    const fillTestData = () => {
      formData.value.email = '<EMAIL>'
      formData.value.password = '123456'
    }

    // 测试后端连接
    const testConnection = async () => {
      connectionStatus.value = { type: 'info', message: '正在测试连接...' }

      try {
        const response = await commonAPI.healthCheck()
        console.log('健康检查响应:', response.data)
        connectionStatus.value = {
          type: 'success',
          message: '后端连接成功！服务器状态正常'
        }
      } catch (error) {
        const errorInfo = handleApiError(error)
        console.error('连接测试失败:', errorInfo)
        connectionStatus.value = {
          type: 'error',
          message: `连接失败: ${errorInfo.message}`
        }
      }
    }
    
    return {
      authStore,
      isRegisterMode,
      formData,
      registerData,
      isFormValid,
      isRegisterFormValid,
      connectionStatus,
      handleSubmit,
      handleRegister,
      switchToRegister,
      switchToLogin,
      fillTestData,
      testConnection
    }
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-card {
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
}

.login-title {
  text-align: center;
  color: #333;
  margin-bottom: 8px;
  font-size: 28px;
  font-weight: 600;
}

.login-subtitle {
  text-align: center;
  color: #666;
  margin-bottom: 32px;
  font-size: 16px;
}

.login-form {
  margin-bottom: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  color: #333;
  font-weight: 500;
}

.form-group input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s;
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
}

.form-group input:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

.form-options {
  margin-bottom: 24px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  color: #666;
  cursor: pointer;
}

.checkbox-label input {
  margin-right: 8px;
  width: auto;
}

.login-btn {
  width: 100%;
  padding: 14px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: opacity 0.3s;
}

.login-btn:hover:not(:disabled) {
  opacity: 0.9;
}

.login-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.form-footer {
  text-align: center;
  margin-top: 20px;
}

.link-btn {
  background: none;
  border: none;
  color: #667eea;
  cursor: pointer;
  text-decoration: underline;
  font-size: inherit;
}

.link-btn:hover:not(:disabled) {
  color: #764ba2;
}

.error-message {
  background: #fee;
  color: #c33;
  padding: 12px 16px;
  border-radius: 8px;
  margin-top: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.close-btn {
  background: none;
  border: none;
  color: #c33;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
}

.test-info {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-top: 20px;
  text-align: center;
}

.test-info h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 16px;
}

.test-info p {
  margin: 4px 0;
  color: #666;
  font-size: 14px;
}

.test-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  margin-top: 8px;
  font-size: 14px;
}

.test-btn:hover {
  background: #218838;
}

.connection-status {
  margin-top: 16px;
  padding: 12px;
  border-radius: 8px;
  text-align: center;
  font-size: 14px;
}

.connection-status.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.connection-status.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.connection-status.info {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}
</style>
