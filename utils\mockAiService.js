// 模拟AI服务，用于演示和测试
class MockAIService {
  constructor() {
    this.responses = [
      "Hello! I'm excited to help you practice English. What would you like to talk about today?",
      "That's interesting! Can you tell me more about that?",
      "Great! Your English is improving. Let's continue our conversation.",
      "I understand what you mean. How do you feel about this topic?",
      "Excellent question! Let me think about that for a moment.",
      "That's a wonderful way to look at it. What made you think of that?",
      "I appreciate you sharing that with me. Can you give me an example?",
      "You're doing really well with your English! Keep up the good work.",
      "That's a complex topic. What's your opinion on this matter?",
      "I see your point. Have you experienced something similar before?"
    ];
    
    this.grammarResponses = [
      "Your grammar looks good! Just a small suggestion: instead of 'I am go to school', try 'I am going to school' or 'I go to school'.",
      "Great sentence structure! One minor correction: 'He don't like pizza' should be 'He doesn't like pizza'.",
      "Excellent! Your grammar is correct. Well done!",
      "Almost perfect! Consider changing 'I have went' to 'I have gone' for proper past participle usage.",
      "Good effort! Remember that 'much' is used with uncountable nouns, while 'many' is used with countable nouns."
    ];
  }

  isAvailable() {
    return true;
  }

  async generateChatResponse(message, options = {}) {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 200 + Math.random() * 300));

    const {
      userLevel = 'intermediate',
      conversationHistory = []
    } = options;

    // 根据用户等级调整回复
    let response;
    if (userLevel === 'beginner') {
      response = this.getBeginnerResponse(message);
    } else if (userLevel === 'advanced') {
      response = this.getAdvancedResponse(message);
    } else {
      response = this.getIntermediateResponse(message);
    }

    // 如果是问候语，给出特定回复
    if (this.isGreeting(message)) {
      response = "Hello! It's wonderful to meet you. I'm here to help you practice English conversation. What would you like to talk about today? We could discuss your hobbies, daily life, or any topic that interests you!";
    }

    return {
      content: response,
      metadata: {
        model: 'mock-ai',
        responseTime: 500 + Math.random() * 1000,
        tokens: this.estimateTokens(message + response),
        confidence: 0.85 + Math.random() * 0.1
      }
    };
  }

  async checkGrammar(text, options = {}) {
    await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));

    const hasErrors = Math.random() > 0.7; // 30% chance of having errors
    
    let analysis;
    if (hasErrors) {
      analysis = `1. 是否有语法错误：是
2. 纠正后的文本：${this.generateCorrectedText(text)}
3. 错误解释：${this.getRandomGrammarExplanation()}
4. 建议：多练习时态和主谓一致，建议阅读更多英语材料来提高语感。`;
    } else {
      analysis = `1. 是否有语法错误：否
2. 纠正后的文本：文本语法正确，无需修改
3. 错误解释：无语法错误
4. 建议：语法使用得很好！可以尝试使用更丰富的词汇来提升表达水平。`;
    }

    return {
      originalText: text,
      analysis,
      timestamp: new Date()
    };
  }

  async reviewEssay(essay, options = {}) {
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 1500));

    const { topic = '', level = 'intermediate' } = options;

    const score = 6 + Math.random() * 3; // 6-9分
    
    const review = `作文评分报告

总体评分：${score.toFixed(1)}/10分

各项评分：
1. 语法准确性：${(score - 0.5 + Math.random()).toFixed(1)}/10
   - 整体语法使用较好，时态运用基本正确
   - 建议注意主谓一致和冠词的使用

2. 词汇使用：${(score + Math.random() * 0.5).toFixed(1)}/10
   - 词汇选择恰当，表达清晰
   - 可以尝试使用更多高级词汇来丰富表达

3. 文章结构：${(score - 0.3 + Math.random() * 0.6).toFixed(1)}/10
   - 文章结构清晰，逻辑性强
   - 段落之间的过渡可以更加自然

4. 内容质量：${(score + Math.random() * 0.3).toFixed(1)}/10
   - 内容充实，观点明确
   - 论证有力，例子恰当

优点总结：
- 文章主题明确，论点清晰
- 语言表达流畅，易于理解
- 结构合理，层次分明

需要改进的地方：
- 可以增加更多具体的例子来支持观点
- 注意一些细节的语法错误
- 尝试使用更丰富的连接词

修改建议：
- 多阅读英语范文，学习地道的表达方式
- 练习使用不同的句型结构
- 注意检查语法和拼写错误`;

    return {
      originalEssay: essay,
      review,
      topic,
      level,
      timestamp: new Date()
    };
  }

  getBeginnerResponse(message) {
    const responses = [
      "Hello! Nice to meet you. How are you today?",
      "That's good! Can you tell me about your family?",
      "I like that too! What is your favorite color?",
      "Very good! Do you like to read books?",
      "Great! What do you do every day?"
    ];
    return responses[Math.floor(Math.random() * responses.length)];
  }

  getIntermediateResponse(message) {
    return this.responses[Math.floor(Math.random() * this.responses.length)];
  }

  getAdvancedResponse(message) {
    const responses = [
      "That's a fascinating perspective! I'd love to delve deeper into your thoughts on this matter. What underlying factors do you think contribute to this phenomenon?",
      "Your analysis is quite insightful. Have you considered the broader implications of this issue on society as a whole?",
      "I appreciate the nuanced way you've approached this topic. Could you elaborate on the potential counterarguments to your position?",
      "That's an excellent point that demonstrates sophisticated thinking. How might this concept apply to other contexts or situations?",
      "Your reasoning is compelling. What evidence or experiences have shaped your understanding of this subject?"
    ];
    return responses[Math.floor(Math.random() * responses.length)];
  }

  isGreeting(message) {
    const greetings = ['hello', 'hi', 'hey', 'good morning', 'good afternoon', 'good evening'];
    return greetings.some(greeting => message.toLowerCase().includes(greeting));
  }

  generateCorrectedText(text) {
    // 简单的文本修正示例
    return text.replace(/\bi am go\b/gi, 'I am going')
               .replace(/\bhe don't\b/gi, 'he doesn\'t')
               .replace(/\bmuch peoples\b/gi, 'many people');
  }

  getRandomGrammarExplanation() {
    const explanations = [
      "注意动词时态的一致性，现在进行时需要使用be动词+动词ing形式",
      "第三人称单数的否定形式应该使用doesn't而不是don't",
      "people是复数名词，应该使用many而不是much来修饰",
      "注意主谓一致，单数主语配单数动词，复数主语配复数动词"
    ];
    return explanations[Math.floor(Math.random() * explanations.length)];
  }

  estimateTokens(text) {
    return Math.ceil(text.length / 4);
  }

  getStatus() {
    return {
      available: true,
      model: 'mock-ai',
      timestamp: new Date()
    };
  }
}

module.exports = new MockAIService();
