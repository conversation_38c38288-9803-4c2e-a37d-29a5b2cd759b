# 服务器配置
PORT=3000
NODE_ENV=development

# MongoDB 数据库配置
MONGODB_URI=mongodb://localhost:27017/englishlearning
MONGODB_TEST_URI=mongodb://localhost:27017/englishlearning_test

# JWT 认证配置
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=7d

# Google Generative AI 配置
GOOGLE_AI_API_KEY=your_google_ai_api_key_here

# CORS 配置
CORS_ORIGIN=http://localhost:3000

# 日志配置
LOG_LEVEL=info

# 文件上传配置
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads

# 邮件配置 (可选，用于密码重置等功能)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_email_password

# Redis 配置 (可选，用于缓存和会话管理)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# 安全配置
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
