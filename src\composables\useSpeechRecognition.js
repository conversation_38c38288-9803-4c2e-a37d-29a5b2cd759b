import { ref, onUnmounted } from 'vue'
import { useSettingsStore } from '../stores/settings'

export function useSpeechRecognition(options = {}) {
  const settingsStore = useSettingsStore()
  
  const isListening = ref(false)
  const isSupported = ref(false)
  const transcript = ref('')
  const confidence = ref(0)
  const error = ref(null)
  
  let recognition = null
  
  // 检查浏览器支持
  const checkSupport = () => {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
    isSupported.value = !!SpeechRecognition
    return SpeechRecognition
  }
  
  // 初始化语音识别
  const initRecognition = () => {
    const SpeechRecognition = checkSupport()
    if (!SpeechRecognition) {
      console.warn('当前浏览器不支持语音识别')
      return null
    }
    
    recognition = new SpeechRecognition()
    
    // 配置识别参数
    recognition.continuous = settingsStore.speechRecognition.continuous
    recognition.interimResults = settingsStore.speechRecognition.interimResults
    recognition.lang = settingsStore.speechRecognition.language
    recognition.maxAlternatives = 1
    
    // 事件监听
    recognition.onstart = () => {
      isListening.value = true
      error.value = null
      options.onStart?.()
    }
    
    recognition.onresult = (event) => {
      let finalTranscript = ''
      let interimTranscript = ''
      
      for (let i = event.resultIndex; i < event.results.length; i++) {
        const result = event.results[i]
        const transcriptText = result[0].transcript
        
        if (result.isFinal) {
          finalTranscript += transcriptText
          confidence.value = result[0].confidence
        } else {
          interimTranscript += transcriptText
        }
      }
      
      if (finalTranscript) {
        transcript.value = finalTranscript
        options.onResult?.(finalTranscript, confidence.value)
      } else if (interimTranscript) {
        options.onInterim?.(interimTranscript)
      }
    }
    
    recognition.onerror = (event) => {
      error.value = event.error
      isListening.value = false
      
      console.error('语音识别错误:', event.error)
      
      // 处理不同类型的错误
      switch (event.error) {
        case 'no-speech':
          options.onError?.('没有检测到语音，请重试')
          break
        case 'audio-capture':
          options.onError?.('无法访问麦克风，请检查权限设置')
          break
        case 'not-allowed':
          options.onError?.('麦克风权限被拒绝，请在浏览器设置中允许麦克风访问')
          break
        case 'network':
          options.onError?.('网络错误，请检查网络连接')
          break
        case 'service-not-allowed':
          options.onError?.('语音识别服务不可用')
          break
        default:
          options.onError?.(`语音识别错误: ${event.error}`)
      }
    }
    
    recognition.onend = () => {
      isListening.value = false
      options.onEnd?.()
    }
    
    return recognition
  }
  
  // 开始识别
  const start = () => {
    if (!isSupported.value) {
      options.onError?.('当前浏览器不支持语音识别')
      return
    }
    
    if (isListening.value) {
      return
    }
    
    if (!recognition) {
      recognition = initRecognition()
    }
    
    if (recognition) {
      try {
        recognition.start()
      } catch (err) {
        console.error('启动语音识别失败:', err)
        options.onError?.('启动语音识别失败')
      }
    }
  }
  
  // 停止识别
  const stop = () => {
    if (recognition && isListening.value) {
      recognition.stop()
    }
  }
  
  // 中止识别
  const abort = () => {
    if (recognition && isListening.value) {
      recognition.abort()
    }
  }
  
  // 重置状态
  const reset = () => {
    transcript.value = ''
    confidence.value = 0
    error.value = null
  }
  
  // 更新设置
  const updateSettings = (newSettings) => {
    if (recognition) {
      recognition.continuous = newSettings.continuous ?? recognition.continuous
      recognition.interimResults = newSettings.interimResults ?? recognition.interimResults
      recognition.lang = newSettings.language ?? recognition.lang
    }
  }
  
  // 组件卸载时清理
  onUnmounted(() => {
    if (recognition) {
      recognition.onstart = null
      recognition.onresult = null
      recognition.onerror = null
      recognition.onend = null
      
      if (isListening.value) {
        recognition.abort()
      }
    }
  })
  
  // 初始化
  checkSupport()
  
  return {
    // 状态
    isListening,
    isSupported,
    transcript,
    confidence,
    error,
    
    // 方法
    start,
    stop,
    abort,
    reset,
    updateSettings
  }
}

// 语音识别权限检查
export async function checkMicrophonePermission() {
  try {
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      return { state: 'unsupported', message: '当前浏览器不支持麦克风访问' }
    }
    
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
    stream.getTracks().forEach(track => track.stop())
    
    return { state: 'granted', message: '麦克风权限已授予' }
  } catch (error) {
    if (error.name === 'NotAllowedError') {
      return { state: 'denied', message: '麦克风权限被拒绝' }
    } else if (error.name === 'NotFoundError') {
      return { state: 'not-found', message: '未找到麦克风设备' }
    } else {
      return { state: 'error', message: `麦克风访问错误: ${error.message}` }
    }
  }
}

// 获取可用的语音识别语言
export function getSupportedLanguages() {
  return [
    { code: 'en-US', name: '英语 (美国)' },
    { code: 'en-GB', name: '英语 (英国)' },
    { code: 'en-AU', name: '英语 (澳大利亚)' },
    { code: 'en-CA', name: '英语 (加拿大)' },
    { code: 'zh-CN', name: '中文 (简体)' },
    { code: 'zh-TW', name: '中文 (繁体)' },
    { code: 'zh-HK', name: '中文 (香港)' },
    { code: 'ja-JP', name: '日语' },
    { code: 'ko-KR', name: '韩语' },
    { code: 'fr-FR', name: '法语' },
    { code: 'de-DE', name: '德语' },
    { code: 'es-ES', name: '西班牙语' },
    { code: 'it-IT', name: '意大利语' },
    { code: 'pt-BR', name: '葡萄牙语 (巴西)' },
    { code: 'ru-RU', name: '俄语' },
    { code: 'ar-SA', name: '阿拉伯语' },
    { code: 'hi-IN', name: '印地语' },
    { code: 'th-TH', name: '泰语' },
    { code: 'vi-VN', name: '越南语' }
  ]
}
