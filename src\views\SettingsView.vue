<template>
  <div class="settings-view">
    <div class="settings-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>设置</h1>
        <button @click="resetAllSettings" class="reset-btn">
          重置所有设置
        </button>
      </div>
      
      <!-- 设置分组 -->
      <div class="settings-groups">
        <!-- 外观设置 -->
        <div class="settings-group">
          <h2 class="group-title">
            <span class="group-icon">🎨</span>
            外观设置
          </h2>
          
          <div class="setting-item">
            <div class="setting-info">
              <label class="setting-label">主题模式</label>
              <p class="setting-description">选择浅色或深色主题</p>
            </div>
            <div class="setting-control">
              <div class="theme-selector">
                <button 
                  @click="updateTheme('light')"
                  class="theme-option"
                  :class="{ active: theme === 'light' }"
                >
                  <span class="theme-icon">☀️</span>
                  <span>浅色</span>
                </button>
                <button 
                  @click="updateTheme('dark')"
                  class="theme-option"
                  :class="{ active: theme === 'dark' }"
                >
                  <span class="theme-icon">🌙</span>
                  <span>深色</span>
                </button>
              </div>
            </div>
          </div>
          
          <div class="setting-item">
            <div class="setting-info">
              <label class="setting-label">界面语言</label>
              <p class="setting-description">选择界面显示语言</p>
            </div>
            <div class="setting-control">
              <select 
                v-model="language" 
                @change="updateLanguage"
                class="select-input"
              >
                <option value="zh-CN">简体中文</option>
                <option value="zh-TW">繁体中文</option>
                <option value="en-US">English</option>
              </select>
            </div>
          </div>
        </div>
        
        <!-- 语音设置 -->
        <div class="settings-group">
          <h2 class="group-title">
            <span class="group-icon">🔊</span>
            语音设置
          </h2>
          
          <div class="setting-item">
            <div class="setting-info">
              <label class="setting-label">启用语音合成</label>
              <p class="setting-description">AI回复时自动朗读消息</p>
            </div>
            <div class="setting-control">
              <label class="switch">
                <input 
                  type="checkbox" 
                  v-model="voiceSettings.enabled"
                  @change="updateVoiceSettings"
                >
                <span class="slider"></span>
              </label>
            </div>
          </div>
          
          <div class="setting-item">
            <div class="setting-info">
              <label class="setting-label">自动播放</label>
              <p class="setting-description">AI回复后自动朗读</p>
            </div>
            <div class="setting-control">
              <label class="switch">
                <input 
                  type="checkbox" 
                  v-model="voiceSettings.autoPlay"
                  @change="updateVoiceSettings"
                  :disabled="!voiceSettings.enabled"
                >
                <span class="slider"></span>
              </label>
            </div>
          </div>
          
          <div class="setting-item">
            <div class="setting-info">
              <label class="setting-label">语音选择</label>
              <p class="setting-description">选择朗读语音</p>
            </div>
            <div class="setting-control">
              <select 
                v-model="voiceSettings.voice" 
                @change="updateVoiceSettings"
                class="select-input"
                :disabled="!voiceSettings.enabled"
              >
                <option value="">默认语音</option>
                <option 
                  v-for="voice in englishVoices" 
                  :key="voice.name"
                  :value="voice.name"
                >
                  {{ voice.name }} ({{ voice.lang }})
                </option>
              </select>
              <button 
                @click="testVoice"
                class="test-btn"
                :disabled="!voiceSettings.enabled || isTesting"
              >
                {{ isTesting ? '测试中...' : '测试' }}
              </button>
            </div>
          </div>
          
          <div class="setting-item">
            <div class="setting-info">
              <label class="setting-label">语速</label>
              <p class="setting-description">调整朗读速度 ({{ voiceSettings.rate }}x)</p>
            </div>
            <div class="setting-control">
              <input 
                type="range" 
                min="0.5" 
                max="2" 
                step="0.1"
                v-model.number="voiceSettings.rate"
                @input="updateVoiceSettings"
                class="range-input"
                :disabled="!voiceSettings.enabled"
              >
            </div>
          </div>
          
          <div class="setting-item">
            <div class="setting-info">
              <label class="setting-label">音调</label>
              <p class="setting-description">调整朗读音调 ({{ voiceSettings.pitch }})</p>
            </div>
            <div class="setting-control">
              <input 
                type="range" 
                min="0.5" 
                max="2" 
                step="0.1"
                v-model.number="voiceSettings.pitch"
                @input="updateVoiceSettings"
                class="range-input"
                :disabled="!voiceSettings.enabled"
              >
            </div>
          </div>
          
          <div class="setting-item">
            <div class="setting-info">
              <label class="setting-label">音量</label>
              <p class="setting-description">调整朗读音量 ({{ Math.round(voiceSettings.volume * 100) }}%)</p>
            </div>
            <div class="setting-control">
              <input 
                type="range" 
                min="0" 
                max="1" 
                step="0.1"
                v-model.number="voiceSettings.volume"
                @input="updateVoiceSettings"
                class="range-input"
                :disabled="!voiceSettings.enabled"
              >
            </div>
          </div>
        </div>

        <!-- 语音识别设置 -->
        <div class="settings-group">
          <h2 class="group-title">
            <span class="group-icon">🎤</span>
            语音识别设置
          </h2>

          <div class="setting-item">
            <div class="setting-info">
              <label class="setting-label">启用语音识别</label>
              <p class="setting-description">使用麦克风进行语音输入</p>
            </div>
            <div class="setting-control">
              <label class="switch">
                <input
                  type="checkbox"
                  v-model="speechRecognition.enabled"
                  @change="updateSpeechRecognition"
                >
                <span class="slider"></span>
              </label>
            </div>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <label class="setting-label">识别语言</label>
              <p class="setting-description">选择语音识别的语言</p>
            </div>
            <div class="setting-control">
              <select
                v-model="speechRecognition.language"
                @change="updateSpeechRecognition"
                class="select-input"
                :disabled="!speechRecognition.enabled"
              >
                <option
                  v-for="lang in recognitionLanguages"
                  :key="lang.code"
                  :value="lang.code"
                >
                  {{ lang.name }}
                </option>
              </select>
            </div>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <label class="setting-label">连续识别</label>
              <p class="setting-description">持续监听语音输入</p>
            </div>
            <div class="setting-control">
              <label class="switch">
                <input
                  type="checkbox"
                  v-model="speechRecognition.continuous"
                  @change="updateSpeechRecognition"
                  :disabled="!speechRecognition.enabled"
                >
                <span class="slider"></span>
              </label>
            </div>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <label class="setting-label">实时结果</label>
              <p class="setting-description">显示识别过程中的临时结果</p>
            </div>
            <div class="setting-control">
              <label class="switch">
                <input
                  type="checkbox"
                  v-model="speechRecognition.interimResults"
                  @change="updateSpeechRecognition"
                  :disabled="!speechRecognition.enabled"
                >
                <span class="slider"></span>
              </label>
            </div>
          </div>
        </div>

        <!-- AI设置 -->
        <div class="settings-group">
          <h2 class="group-title">
            <span class="group-icon">🤖</span>
            AI设置
          </h2>

          <div class="setting-item">
            <div class="setting-info">
              <label class="setting-label">AI模型</label>
              <p class="setting-description">选择使用的AI模型</p>
            </div>
            <div class="setting-control">
              <select
                v-model="aiSettings.model"
                @change="updateAiSettings"
                class="select-input"
              >
                <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                <option value="gpt-4">GPT-4</option>
                <option value="claude-3">Claude-3</option>
              </select>
            </div>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <label class="setting-label">创造性</label>
              <p class="setting-description">调整AI回复的创造性 ({{ aiSettings.temperature }})</p>
            </div>
            <div class="setting-control">
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                v-model.number="aiSettings.temperature"
                @input="updateAiSettings"
                class="range-input"
              >
            </div>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <label class="setting-label">最大回复长度</label>
              <p class="setting-description">限制AI回复的最大字数 ({{ aiSettings.maxTokens }})</p>
            </div>
            <div class="setting-control">
              <input
                type="range"
                min="100"
                max="2000"
                step="100"
                v-model.number="aiSettings.maxTokens"
                @input="updateAiSettings"
                class="range-input"
              >
            </div>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <label class="setting-label">系统提示词</label>
              <p class="setting-description">设置AI的角色和行为</p>
            </div>
            <div class="setting-control">
              <textarea
                v-model="aiSettings.systemPrompt"
                @input="updateAiSettings"
                class="textarea-input"
                rows="4"
                placeholder="输入系统提示词..."
              ></textarea>
            </div>
          </div>
        </div>

        <!-- 数据管理 -->
        <div class="settings-group">
          <h2 class="group-title">
            <span class="group-icon">💾</span>
            数据管理
          </h2>

          <div class="setting-item">
            <div class="setting-info">
              <label class="setting-label">导出数据</label>
              <p class="setting-description">导出所有对话历史和设置</p>
            </div>
            <div class="setting-control">
              <button @click="exportData" class="action-btn">
                📤 导出数据
              </button>
            </div>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <label class="setting-label">导入数据</label>
              <p class="setting-description">从文件导入对话历史和设置</p>
            </div>
            <div class="setting-control">
              <input
                type="file"
                @change="importData"
                accept=".json"
                class="file-input"
                ref="fileInput"
              >
              <button @click="$refs.fileInput.click()" class="action-btn">
                📥 选择文件
              </button>
            </div>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <label class="setting-label">清除所有数据</label>
              <p class="setting-description">删除所有对话历史和重置设置</p>
            </div>
            <div class="setting-control">
              <button @click="clearAllData" class="action-btn danger">
                🗑️ 清除数据
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useSettingsStore } from '../stores/settings'
import { useChatStore } from '../stores/chat'
import { useSpeechSynthesis } from '../composables/useSpeechSynthesis'
import { getSupportedLanguages } from '../composables/useSpeechRecognition'

export default {
  name: 'SettingsView',
  setup() {
    const settingsStore = useSettingsStore()
    const chatStore = useChatStore()
    const { testVoice: testSpeechVoice, voices, getVoicesForLanguage } = useSpeechSynthesis()

    const isTesting = ref(false)
    const fileInput = ref(null)

    // 计算属性
    const theme = computed(() => settingsStore.theme)
    const language = computed({
      get: () => settingsStore.language,
      set: (value) => settingsStore.updateLanguage(value)
    })

    const voiceSettings = computed(() => settingsStore.voiceSettings)
    const speechRecognition = computed(() => settingsStore.speechRecognition)
    const aiSettings = computed(() => settingsStore.aiSettings)

    const englishVoices = computed(() => {
      return getVoicesForLanguage('en')
    })

    const recognitionLanguages = getSupportedLanguages()

    // 方法
    const updateTheme = (newTheme) => {
      settingsStore.updateTheme(newTheme)
    }

    const updateLanguage = () => {
      // 语言更新已通过computed属性处理
    }

    const updateVoiceSettings = () => {
      settingsStore.updateVoiceSettings(voiceSettings.value)
    }

    const updateSpeechRecognition = () => {
      settingsStore.updateSpeechRecognition(speechRecognition.value)
    }

    const updateAiSettings = () => {
      settingsStore.updateAiSettings(aiSettings.value)
    }

    const testVoice = async () => {
      if (isTesting.value) return

      isTesting.value = true
      try {
        const testText = "Hello! This is a test of the speech synthesis. How does it sound?"
        await testSpeechVoice(voiceSettings.value.voice, testText)
      } catch (error) {
        console.error('语音测试失败:', error)
        alert('语音测试失败，请检查设置')
      } finally {
        isTesting.value = false
      }
    }

    const resetAllSettings = () => {
      if (confirm('确定要重置所有设置吗？此操作无法撤销。')) {
        settingsStore.resetSettings()
        alert('设置已重置')
      }
    }

    const exportData = () => {
      try {
        const data = {
          conversations: chatStore.conversations,
          settings: {
            theme: settingsStore.theme,
            language: settingsStore.language,
            voiceSettings: settingsStore.voiceSettings,
            speechRecognition: settingsStore.speechRecognition,
            aiSettings: settingsStore.aiSettings
          },
          exportDate: new Date().toISOString()
        }

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `english-chat-data-${new Date().toISOString().split('T')[0]}.json`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)

        alert('数据导出成功')
      } catch (error) {
        console.error('导出数据失败:', error)
        alert('导出数据失败')
      }
    }

    const importData = (event) => {
      const file = event.target.files[0]
      if (!file) return

      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const data = JSON.parse(e.target.result)

          if (confirm('导入数据将覆盖当前所有数据，确定继续吗？')) {
            // 导入对话数据
            if (data.conversations) {
              // 清除现有对话
              chatStore.conversations.forEach(conv => {
                chatStore.deleteConversation(conv.id)
              })

              // 导入新对话
              data.conversations.forEach(conv => {
                chatStore.conversations.push(conv)
              })
            }

            // 导入设置
            if (data.settings) {
              if (data.settings.theme) settingsStore.updateTheme(data.settings.theme)
              if (data.settings.language) settingsStore.updateLanguage(data.settings.language)
              if (data.settings.voiceSettings) settingsStore.updateVoiceSettings(data.settings.voiceSettings)
              if (data.settings.speechRecognition) settingsStore.updateSpeechRecognition(data.settings.speechRecognition)
              if (data.settings.aiSettings) settingsStore.updateAiSettings(data.settings.aiSettings)
            }

            alert('数据导入成功')
          }
        } catch (error) {
          console.error('导入数据失败:', error)
          alert('导入数据失败，请检查文件格式')
        }
      }
      reader.readAsText(file)

      // 清除文件输入
      event.target.value = ''
    }

    const clearAllData = () => {
      if (confirm('确定要清除所有数据吗？包括对话历史和设置，此操作无法撤销。')) {
        // 清除对话数据
        chatStore.conversations.forEach(conv => {
          chatStore.deleteConversation(conv.id)
        })

        // 重置设置
        settingsStore.resetSettings()

        // 清除localStorage
        localStorage.clear()

        alert('所有数据已清除')
      }
    }

    onMounted(() => {
      // 确保语音列表已加载
      if (voices.value.length === 0) {
        setTimeout(() => {
          // 语音可能需要异步加载
        }, 1000)
      }
    })

    return {
      theme,
      language,
      voiceSettings,
      speechRecognition,
      aiSettings,
      englishVoices,
      recognitionLanguages,
      isTesting,
      fileInput,
      updateTheme,
      updateLanguage,
      updateVoiceSettings,
      updateSpeechRecognition,
      updateAiSettings,
      testVoice,
      resetAllSettings,
      exportData,
      importData,
      clearAllData
    }
  }
}
</script>

<style scoped>
.settings-view {
  min-height: calc(100vh - 64px);
  background-color: var(--background-color);
}

.settings-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.page-header h1 {
  color: var(--text-primary);
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
}

.reset-btn {
  background-color: var(--error-color);
  color: white;
  border: none;
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.reset-btn:hover {
  background-color: #dc2626;
}

.settings-groups {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.settings-group {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  padding: 1.5rem;
}

.group-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 0 1.5rem 0;
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
}

.group-icon {
  font-size: 1.5rem;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1rem 0;
  border-bottom: 1px solid var(--border-color);
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-info {
  flex: 1;
  margin-right: 1rem;
}

.setting-label {
  display: block;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.setting-description {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin: 0;
  line-height: 1.4;
}

.setting-control {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
}

/* 主题选择器 */
.theme-selector {
  display: flex;
  gap: 0.5rem;
}

.theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  padding: 0.75rem;
  border: 2px solid var(--border-color);
  border-radius: 0.5rem;
  background: var(--background-color);
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
}

.theme-option:hover {
  border-color: var(--primary-color);
}

.theme-option.active {
  border-color: var(--primary-color);
  background-color: var(--primary-color);
  color: white;
}

.theme-icon {
  font-size: 1.25rem;
}

/* 开关样式 */
.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--border-color);
  transition: 0.3s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: var(--primary-color);
}

input:checked + .slider:before {
  transform: translateX(26px);
}

input:disabled + .slider {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 输入框样式 */
.select-input,
.textarea-input {
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  padding: 0.5rem;
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.select-input {
  min-width: 150px;
}

.textarea-input {
  width: 100%;
  resize: vertical;
  font-family: inherit;
  line-height: 1.4;
}

.select-input:focus,
.textarea-input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.select-input:disabled,
.textarea-input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 滑块样式 */
.range-input {
  width: 150px;
  height: 6px;
  border-radius: 3px;
  background: var(--border-color);
  outline: none;
  -webkit-appearance: none;
}

.range-input::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
}

.range-input::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  border: none;
}

.range-input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 按钮样式 */
.test-btn,
.action-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color 0.2s ease;
  white-space: nowrap;
}

.test-btn:hover,
.action-btn:hover {
  background-color: var(--primary-hover);
}

.test-btn:disabled,
.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-btn.danger {
  background-color: var(--error-color);
}

.action-btn.danger:hover {
  background-color: #dc2626;
}

/* 文件输入 */
.file-input {
  display: none;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .settings-container {
    padding: 1rem 0.5rem;
  }

  .page-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .page-header h1 {
    font-size: 1.5rem;
    text-align: center;
  }

  .setting-item {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .setting-info {
    margin-right: 0;
  }

  .setting-control {
    justify-content: flex-start;
  }

  .theme-selector {
    justify-content: center;
  }

  .range-input {
    width: 100%;
  }

  .select-input {
    min-width: auto;
    width: 100%;
  }
}

/* 深色模式特定样式 */
[data-theme="dark"] .theme-option {
  background: var(--surface-color);
}

[data-theme="dark"] .slider:before {
  background-color: var(--surface-color);
}
</style>
