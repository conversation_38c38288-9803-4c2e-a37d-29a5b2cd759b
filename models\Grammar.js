const mongoose = require('mongoose');

// 语法规则Schema
const grammarSchema = new mongoose.Schema({
  // 基本信息
  title: {
    type: String,
    required: [true, '语法标题是必需的'],
    trim: true,
    maxlength: [200, '标题不能超过200个字符'],
    index: true
  },
  
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  
  // 语法分类
  category: {
    type: String,
    required: true,
    enum: [
      'tenses', 'modals', 'conditionals', 'passive-voice', 
      'reported-speech', 'articles', 'prepositions', 
      'conjunctions', 'adjectives', 'adverbs', 'nouns', 
      'pronouns', 'sentence-structure', 'questions', 'negation'
    ],
    index: true
  },
  
  subcategory: {
    type: String,
    maxlength: [100, '子分类不能超过100个字符']
  },
  
  // 难度等级
  level: {
    type: String,
    required: true,
    enum: ['beginner', 'intermediate', 'advanced'],
    index: true
  },
  
  // 语法内容
  content: {
    // 规则描述
    description: {
      type: String,
      required: [true, '语法描述是必需的'],
      maxlength: [2000, '描述不能超过2000个字符']
    },
    
    // 中文解释
    chineseExplanation: {
      type: String,
      maxlength: [2000, '中文解释不能超过2000个字符']
    },
    
    // 语法结构
    structure: {
      type: String,
      maxlength: [500, '语法结构不能超过500个字符']
    },
    
    // 使用场景
    usage: [{
      scenario: {
        type: String,
        required: true,
        maxlength: [200, '使用场景不能超过200个字符']
      },
      explanation: {
        type: String,
        maxlength: [500, '场景解释不能超过500个字符']
      }
    }],
    
    // 例句
    examples: [{
      sentence: {
        type: String,
        required: true,
        maxlength: [300, '例句不能超过300个字符']
      },
      translation: {
        type: String,
        maxlength: [300, '翻译不能超过300个字符']
      },
      explanation: {
        type: String,
        maxlength: [500, '例句解释不能超过500个字符']
      },
      isCorrect: {
        type: Boolean,
        default: true
      }
    }],
    
    // 常见错误
    commonMistakes: [{
      mistake: {
        type: String,
        required: true,
        maxlength: [300, '错误示例不能超过300个字符']
      },
      correction: {
        type: String,
        required: true,
        maxlength: [300, '正确示例不能超过300个字符']
      },
      explanation: {
        type: String,
        maxlength: [500, '错误解释不能超过500个字符']
      }
    }],
    
    // 练习题
    exercises: [{
      type: {
        type: String,
        enum: ['fill-blank', 'multiple-choice', 'transformation', 'correction'],
        required: true
      },
      question: {
        type: String,
        required: true,
        maxlength: [500, '题目不能超过500个字符']
      },
      options: [String], // 选择题选项
      correctAnswer: {
        type: String,
        required: true
      },
      explanation: {
        type: String,
        maxlength: [500, '解释不能超过500个字符']
      },
      difficulty: {
        type: String,
        enum: ['easy', 'medium', 'hard'],
        default: 'medium'
      }
    }]
  },
  
  // 相关语法点
  related: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Grammar'
  }],
  
  // 前置知识
  prerequisites: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Grammar'
  }],
  
  // 标签
  tags: [{
    type: String,
    trim: true,
    maxlength: [50, '标签不能超过50个字符']
  }],
  
  // 学习统计
  stats: {
    viewCount: {
      type: Number,
      default: 0
    },
    practiceCount: {
      type: Number,
      default: 0
    },
    averageScore: {
      type: Number,
      default: 0
    },
    difficultyRating: {
      type: Number,
      default: 0,
      min: 0,
      max: 5
    }
  },
  
  // 状态
  isPublished: {
    type: Boolean,
    default: true
  },
  
  isActive: {
    type: Boolean,
    default: true
  },
  
  // 创建信息
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  lastUpdatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 索引
grammarSchema.index({ category: 1, level: 1 });
grammarSchema.index({ title: 'text', 'content.description': 'text' });
grammarSchema.index({ tags: 1 });
grammarSchema.index({ isPublished: 1, isActive: 1 });

// 虚拟字段
grammarSchema.virtual('exampleCount').get(function() {
  return this.content.examples.length;
});

grammarSchema.virtual('exerciseCount').get(function() {
  return this.content.exercises.length;
});

// 中间件：生成slug
grammarSchema.pre('save', function(next) {
  if (this.isModified('title')) {
    this.slug = this.title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }
  next();
});

// 静态方法：按分类查找语法
grammarSchema.statics.findByCategory = function(category, level = null) {
  const query = { category, isPublished: true, isActive: true };
  if (level) query.level = level;
  
  return this.find(query).sort({ title: 1 });
};

// 静态方法：搜索语法
grammarSchema.statics.searchGrammar = function(searchTerm, options = {}) {
  const { category, level, limit = 20 } = options;
  
  const query = {
    isPublished: true,
    isActive: true,
    $or: [
      { title: { $regex: searchTerm, $options: 'i' } },
      { 'content.description': { $regex: searchTerm, $options: 'i' } },
      { tags: { $regex: searchTerm, $options: 'i' } }
    ]
  };
  
  if (category) query.category = category;
  if (level) query.level = level;
  
  return this.find(query)
    .sort({ 'stats.viewCount': -1 })
    .limit(limit);
};

// 静态方法：获取推荐语法
grammarSchema.statics.getRecommended = function(level, limit = 10) {
  return this.find({
    level,
    isPublished: true,
    isActive: true
  })
  .sort({ 'stats.averageScore': -1, 'stats.viewCount': -1 })
  .limit(limit);
};

// 实例方法：增加浏览次数
grammarSchema.methods.incrementViewCount = function() {
  this.stats.viewCount += 1;
  return this.save();
};

// 实例方法：增加练习次数
grammarSchema.methods.incrementPracticeCount = function() {
  this.stats.practiceCount += 1;
  return this.save();
};

module.exports = mongoose.model('Grammar', grammarSchema);
