const { GoogleGenerativeAI } = require('@google/generative-ai');
const config = require('../config');
const mockAiService = require('./mockAiService');
const siliconFlowService = require('./siliconFlowService');

class AIService {
  constructor() {
    if (!config.googleAI.apiKey) {
      console.warn('⚠️ Google AI API密钥未配置，AI功能将不可用');
      this.genAI = null;
      return;
    }

    try {
      this.genAI = new GoogleGenerativeAI(config.googleAI.apiKey);
      this.model = this.genAI.getGenerativeModel({ model: 'gemini-pro' });
      console.log('✅ Google AI服务初始化成功');
    } catch (error) {
      console.error('❌ Google AI服务初始化失败:', error);
      this.genAI = null;
    }
  }

  // 检查AI服务是否可用（任一服务可用即可）
  isAvailable() {
    return siliconFlowService.isAvailable() || this.genAI !== null;
  }

  // 生成对话回复
  async generateChatResponse(message, options = {}) {
    // 优先使用硅基流动
    if (siliconFlowService.isAvailable()) {
      try {
        console.log('🚀 使用Silicon Flow AI服务');
        return await siliconFlowService.generateChatResponse(message, options);
      } catch (error) {
        console.error('Silicon Flow失败，尝试Google AI:', error.message);
      }
    }

    // 备选：使用Google AI
    if (this.isAvailable()) {
      try {
        console.log('🔄 使用Google AI服务');
        return await this.generateGoogleAIResponse(message, options);
      } catch (error) {
        console.error('Google AI失败，使用模拟服务:', error.message);
      }
    }

    // 最后备选：使用模拟服务
    console.log('🔄 使用模拟AI服务');
    return await mockAiService.generateChatResponse(message, options);
  }

  // Google AI响应生成（从原方法提取）
  async generateGoogleAIResponse(message, options = {}) {
    try {
      const {
        systemPrompt = '你是一个英语对话练习助手，请用英语与用户对话，帮助用户提高英语口语水平。',
        conversationHistory = [],
        userLevel = 'intermediate',
        maxTokens = 1000
      } = options;

      // 构建完整的提示词
      let fullPrompt = `${systemPrompt}\n\n`;
      
      // 添加用户等级信息
      const levelInstructions = {
        beginner: '请使用简单的词汇和句型，语速较慢，多给予鼓励。',
        intermediate: '请使用中等难度的词汇，适当纠正语法错误。',
        advanced: '请使用丰富的词汇和复杂句型，深入讨论话题。'
      };
      
      fullPrompt += `用户英语水平：${userLevel}。${levelInstructions[userLevel] || levelInstructions.intermediate}\n\n`;

      // 添加对话历史
      if (conversationHistory.length > 0) {
        fullPrompt += '对话历史：\n';
        conversationHistory.slice(-10).forEach(msg => {
          const role = msg.type === 'user' ? '用户' : '助手';
          fullPrompt += `${role}: ${msg.content}\n`;
        });
        fullPrompt += '\n';
      }

      fullPrompt += `用户: ${message}\n助手:`;

      const startTime = Date.now();
      const result = await this.model.generateContent(fullPrompt);
      const responseTime = Date.now() - startTime;

      const response = result.response;
      const text = response.text();

      return {
        content: text.trim(),
        metadata: {
          model: 'gemini-pro',
          responseTime,
          tokens: this.estimateTokens(fullPrompt + text),
          confidence: 0.9 // Gemini不提供置信度，使用默认值
        }
      };

    } catch (error) {
      console.error('Google AI对话生成失败:', error.message);
      console.log('🔄 回退到模拟AI服务');
      return await mockAiService.generateChatResponse(message, options);
    }
  }

  // 语法检查和纠正
  async checkGrammar(text, options = {}) {
    // 优先使用硅基流动
    if (siliconFlowService.isAvailable()) {
      try {
        return await siliconFlowService.checkGrammar(text, options);
      } catch (error) {
        console.error('Silicon Flow语法检查失败，尝试Google AI:', error.message);
      }
    }

    // 备选：使用Google AI
    if (this.isAvailable()) {
      try {
        return await this.checkGrammarWithGoogle(text, options);
      } catch (error) {
        console.error('Google AI语法检查失败，使用模拟服务:', error.message);
      }
    }

    // 最后备选：使用模拟服务
    console.log('🔄 使用模拟AI服务');
    return await mockAiService.checkGrammar(text, options);
  }

  // Google AI语法检查
  async checkGrammarWithGoogle(text, options = {}) {

    try {
      const {
        language = 'english',
        includeExplanation = true
      } = options;

      const prompt = `请检查以下${language}文本的语法错误，并提供纠正建议：

文本: "${text}"

请按以下格式回复：
1. 是否有语法错误：[是/否]
2. 纠正后的文本：[如果有错误，提供纠正版本]
3. 错误解释：[如果有错误且需要解释，说明错误类型和原因]
4. 建议：[提供改进建议]

请用中文解释，但纠正后的文本保持原语言。`;

      const result = await this.model.generateContent(prompt);
      const response = result.response.text();

      return {
        originalText: text,
        analysis: response.trim(),
        timestamp: new Date()
      };

    } catch (error) {
      console.error('Google AI语法检查失败:', error.message);
      console.log('🔄 回退到模拟AI服务');
      return await mockAiService.checkGrammar(text, options);
    }
  }

  // 作文批改
  async reviewEssay(essay, options = {}) {
    // 优先使用硅基流动
    if (siliconFlowService.isAvailable()) {
      try {
        return await siliconFlowService.reviewEssay(essay, options);
      } catch (error) {
        console.error('Silicon Flow作文批改失败，尝试Google AI:', error.message);
      }
    }

    // 备选：使用Google AI
    if (this.isAvailable()) {
      try {
        return await this.reviewEssayWithGoogle(essay, options);
      } catch (error) {
        console.error('Google AI作文批改失败:', error.message);
        throw error;
      }
    }

    throw new Error('AI服务不可用');
  }

  // Google AI作文批改
  async reviewEssayWithGoogle(essay, options = {}) {

    try {
      const {
        topic = '',
        level = 'intermediate',
        criteria = ['grammar', 'vocabulary', 'structure', 'content']
      } = options;

      const prompt = `请对以下英语作文进行详细批改和评分：

作文题目：${topic}
学生水平：${level}
作文内容：
"${essay}"

请按以下标准进行评价：
1. 语法准确性 (Grammar)
2. 词汇使用 (Vocabulary) 
3. 文章结构 (Structure)
4. 内容质量 (Content)

请提供：
1. 总体评分（1-10分）
2. 各项评分和具体建议
3. 优点总结
4. 需要改进的地方
5. 修改建议

请用中文进行评价和建议。`;

      const result = await this.model.generateContent(prompt);
      const response = result.response.text();

      return {
        originalEssay: essay,
        review: response.trim(),
        topic,
        level,
        timestamp: new Date()
      };

    } catch (error) {
      console.error('Google AI作文批改失败:', error.message);
      console.log('🔄 回退到模拟AI服务');
      return await mockAiService.reviewEssay(essay, options);
    }
  }

  // 生成练习题
  async generateExercise(type, options = {}) {
    if (!this.isAvailable()) {
      throw new Error('AI服务不可用');
    }

    try {
      const {
        level = 'intermediate',
        topic = '',
        count = 5
      } = options;

      let prompt = '';

      switch (type) {
        case 'vocabulary':
          prompt = `请生成${count}道英语词汇练习题，难度等级：${level}`;
          if (topic) prompt += `，主题：${topic}`;
          prompt += `。请包含选择题和填空题，并提供答案和解释。`;
          break;

        case 'grammar':
          prompt = `请生成${count}道英语语法练习题，难度等级：${level}`;
          if (topic) prompt += `，语法点：${topic}`;
          prompt += `。请包含不同题型，并提供答案和解释。`;
          break;

        case 'reading':
          prompt = `请生成一篇英语阅读理解材料和${count}道相关问题，难度等级：${level}`;
          if (topic) prompt += `，主题：${topic}`;
          prompt += `。请提供答案和解释。`;
          break;

        default:
          throw new Error('不支持的练习类型');
      }

      const result = await this.model.generateContent(prompt);
      const response = result.response.text();

      return {
        type,
        level,
        topic,
        content: response.trim(),
        timestamp: new Date()
      };

    } catch (error) {
      console.error('练习题生成失败:', error);
      throw new Error(`练习题生成失败: ${error.message}`);
    }
  }

  // 估算token数量（简单估算）
  estimateTokens(text) {
    // 简单估算：英文约4个字符=1个token，中文约1.5个字符=1个token
    const englishChars = (text.match(/[a-zA-Z\s]/g) || []).length;
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const otherChars = text.length - englishChars - chineseChars;
    
    return Math.ceil(englishChars / 4 + chineseChars / 1.5 + otherChars / 3);
  }

  // 获取服务状态
  getStatus() {
    return {
      available: this.isAvailable(),
      model: this.isAvailable() ? 'gemini-pro' : null,
      timestamp: new Date()
    };
  }
}

module.exports = new AIService();
