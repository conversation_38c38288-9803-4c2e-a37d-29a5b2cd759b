const express = require('express');
const router = express.Router();
const aiController = require('../controllers/aiController');
const { authenticateToken, rateLimit } = require('../middleware/auth');

// 应用认证中间件到所有AI路由
router.use(authenticateToken);

// 应用速率限制
const aiRateLimit = rateLimit(60 * 1000, 30); // 1分钟内最多30次请求
const chatRateLimit = rateLimit(60 * 1000, 20); // 1分钟内最多20次对话请求

// AI对话接口（核心功能，匹配前端需求）
router.post('/chat', chatRateLimit, aiController.chat);

// 对话管理
router.get('/conversations', aiController.getConversations);
router.get('/conversations/:conversationId', aiController.getConversation);
router.delete('/conversations/:conversationId', aiController.deleteConversation);

// 语法检查
router.post('/grammar-check', aiRateLimit, aiController.checkGrammar);

// 作文批改
router.post('/essay-review', aiRateLimit, aiController.reviewEssay);

// AI服务状态
router.get('/status', aiController.getStatus);

module.exports = router;
