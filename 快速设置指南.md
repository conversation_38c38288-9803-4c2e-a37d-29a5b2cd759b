# ⚡ 快速设置指南

## 🚀 5分钟快速上手

### 第一步：启动服务
```bash
# 1. 启动后端（新终端窗口）
cd englishbackend
npm start

# 2. 启动前端（新终端窗口）
cd englishfrontend
npm run serve
```

### 第二步：访问应用
- 打开浏览器访问：`http://localhost:8080`
- 注册新账号或登录

### 第三步：基础设置
1. 点击右上角"设置"按钮
2. 根据下面的推荐配置进行设置

## 🎯 推荐配置

### 🔊 语音设置（推荐开启）
```
✅ 启用语音合成：开启
✅ 自动播放：开启
🎚️ 语速：1.0x（可根据个人喜好调整）
🎚️ 音调：1.0
🎚️ 音量：100%
🗣️ 语音选择：选择一个清晰的英语语音
```

### 🎤 语音识别设置
```
✅ 启用语音识别：开启
🌍 识别语言：English (US) 或根据目标口音选择
❌ 连续识别：关闭（避免误触发）
✅ 实时结果：开启
```

### 🤖 AI设置
```
🤖 AI模型：GPT-3.5 Turbo（默认，稳定快速）
🎨 创造性：0.7（平衡创新和一致性）
📏 最大回复长度：1000（适中长度）
📝 系统提示词：见下方场景选择
```

## 📚 常用场景快速配置

### 🏢 商务英语练习
**推荐提示词**：
```
You are a professional business English teacher. Help me practice business conversations including meetings, presentations, and professional communication. Correct my grammar and suggest better business vocabulary. Keep responses concise and practical.
```

**其他设置**：
- 创造性：0.5（更正式、一致）
- 最大回复长度：800
- 语音：选择正式、清晰的语音

### 🗣️ 日常口语练习
**推荐提示词**：
```
You are a friendly English conversation partner. Chat naturally about daily topics like hobbies, food, movies, and current events. Use casual language and help me improve my fluency. Keep conversations fun and engaging.
```

**其他设置**：
- 创造性：0.8（更自然、多样）
- 最大回复长度：600
- 语音：选择友好、自然的语音

### ✈️ 旅游英语准备
**推荐提示词**：
```
You are a travel English assistant. Help me practice conversations for traveling: hotel bookings, restaurant orders, asking directions, shopping, and emergency situations. Provide practical phrases and cultural tips.
```

**其他设置**：
- 创造性：0.6
- 最大回复长度：700
- 自动播放：开启（练习听力）

### 🎓 学术英语提升
**推荐提示词**：
```
You are an academic English tutor. Help me practice formal discussions, presentations, and academic writing. Use sophisticated vocabulary and encourage analytical thinking. Provide detailed feedback on language use.
```

**其他设置**：
- 创造性：0.4（更正式、准确）
- 最大回复长度：1200
- 语音：选择正式、权威的语音

## 🎛️ 个性化调整

### 根据英语水平调整

**初学者（Beginner）**：
```
创造性：0.3（简单、一致的回复）
最大回复长度：400（短句练习）
语速：0.8x（慢速练习听力）
```

**中级（Intermediate）**：
```
创造性：0.7（平衡复杂度）
最大回复长度：800（适中长度）
语速：1.0x（正常语速）
```

**高级（Advanced）**：
```
创造性：0.9（复杂、多样的表达）
最大回复长度：1500（深入讨论）
语速：1.2x（挑战听力）
```

### 根据学习目标调整

**提高听力**：
- ✅ 自动播放：开启
- 🎚️ 语速：从0.8x逐渐提高到1.2x
- 📏 回复长度：增加到1000+

**提高口语**：
- ✅ 语音识别：开启
- ❌ 连续识别：关闭
- 🎤 多使用语音输入练习

**提高词汇**：
- 🎨 创造性：0.8+（获得更多样的表达）
- 📝 提示词：要求AI介绍新词汇
- 📏 回复长度：1000+

**提高语法**：
- 📝 提示词：要求AI纠正语法错误
- 🎨 创造性：0.5（更准确的语法示例）
- 📏 回复长度：适中，重点在准确性

## 🔧 故障排除

### 语音功能不工作
1. **检查浏览器权限**：
   - Chrome：地址栏左侧点击🔒，允许麦克风权限
   - Firefox：地址栏左侧点击🔒，允许麦克风权限

2. **检查设备**：
   - 确保麦克风正常工作
   - 测试其他应用的语音功能

3. **重新设置**：
   - 关闭语音识别，刷新页面，重新开启

### AI回复异常
1. **检查网络连接**：确保能访问互联网
2. **重启后端服务**：
   ```bash
   # 停止后端服务（Ctrl+C）
   # 重新启动
   cd englishbackend
   npm start
   ```
3. **清除浏览器缓存**：刷新页面或清除缓存

### 对话不连续
1. **检查登录状态**：确保已登录
2. **检查对话ID**：在同一对话中继续，不要创建新对话
3. **检查后端日志**：查看终端输出的错误信息

## 💡 使用技巧

### 最大化学习效果
1. **设定学习目标**：每次对话前明确想练习什么
2. **保持对话连续性**：在同一话题上深入讨论
3. **主动使用新词汇**：尝试使用AI教给你的新词
4. **定期调整设置**：根据进步情况调整难度

### 高效练习方法
1. **每日练习**：每天至少15-30分钟
2. **场景轮换**：定期更换不同的提示词和场景
3. **录音回顾**：使用语音功能，注意发音改进
4. **笔记记录**：记录有用的表达和新词汇

### 避免常见错误
1. **不要频繁更换设置**：给每个配置足够的试用时间
2. **不要依赖翻译**：尽量用英语思考和表达
3. **不要害怕犯错**：AI会帮助纠正，错误是学习的一部分
4. **不要单纯聊天**：要有明确的学习目的

## 📊 进度跟踪

### 查看学习统计
- 访问"历史"页面查看：
  - 总对话数
  - 总消息数
  - 平均消息数

### 自我评估
定期问自己：
- 我的词汇量是否在增加？
- 我的语法错误是否在减少？
- 我的表达是否更加自然？
- 我的听力理解是否有提高？

### 调整学习计划
根据进度调整：
- 提高AI创造性（增加难度）
- 更换更具挑战性的提示词
- 增加回复长度
- 提高语音播放速度
