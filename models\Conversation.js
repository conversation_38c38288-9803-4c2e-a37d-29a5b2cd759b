const mongoose = require('mongoose');

// 消息子文档Schema (匹配前端消息结构)
const messageSchema = new mongoose.Schema({
  content: {
    type: String,
    required: [true, '消息内容是必需的'],
    trim: true,
    maxlength: [2000, '消息内容不能超过2000个字符']
  },
  type: {
    type: String,
    required: true,
    enum: ['user', 'ai'],
    index: true
  },
  timestamp: {
    type: Date,
    default: Date.now,
    index: true
  },
  
  // AI消息的额外信息
  aiMetadata: {
    model: String,
    temperature: Number,
    tokens: Number,
    responseTime: Number, // 响应时间(毫秒)
    confidence: Number // AI回复的置信度
  },
  
  // 用户消息的额外信息
  userMetadata: {
    inputMethod: {
      type: String,
      enum: ['text', 'voice'],
      default: 'text'
    },
    originalText: String, // 语音识别的原始文本
    confidence: Number // 语音识别的置信度
  },
  
  // 消息状态
  isEdited: {
    type: Boolean,
    default: false
  },
  editedAt: Date,
  
  // 反馈信息
  feedback: {
    rating: {
      type: Number,
      min: 1,
      max: 5
    },
    comment: String,
    isHelpful: Boolean
  }
}, {
  _id: true,
  timestamps: false
});

// 对话Schema (匹配前端对话结构)
const conversationSchema = new mongoose.Schema({
  // 关联用户
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  
  // 对话基本信息
  title: {
    type: String,
    required: [true, '对话标题是必需的'],
    trim: true,
    maxlength: [200, '对话标题不能超过200个字符']
  },
  
  // 消息列表 (匹配前端messages数组)
  messages: [messageSchema],
  
  // 对话设置
  settings: {
    language: {
      type: String,
      default: 'en-US'
    },
    difficulty: {
      type: String,
      enum: ['beginner', 'intermediate', 'advanced'],
      default: 'intermediate'
    },
    topic: {
      type: String,
      maxlength: [100, '话题不能超过100个字符']
    },
    systemPrompt: String
  },
  
  // 对话统计
  stats: {
    messageCount: {
      type: Number,
      default: 0
    },
    userMessageCount: {
      type: Number,
      default: 0
    },
    aiMessageCount: {
      type: Number,
      default: 0
    },
    totalTokens: {
      type: Number,
      default: 0
    },
    averageResponseTime: {
      type: Number,
      default: 0
    },
    duration: {
      type: Number,
      default: 0 // 对话持续时间(分钟)
    }
  },
  
  // 对话状态
  status: {
    type: String,
    enum: ['active', 'paused', 'completed', 'archived'],
    default: 'active',
    index: true
  },
  
  // 标签和分类
  tags: [{
    type: String,
    trim: true,
    maxlength: [50, '标签不能超过50个字符']
  }],
  
  category: {
    type: String,
    enum: ['general', 'business', 'travel', 'academic', 'casual', 'interview'],
    default: 'general'
  },
  
  // 时间戳 (匹配前端createdAt/updatedAt)
  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  updatedAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  
  // 最后活跃时间
  lastActiveAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  
  // 是否收藏
  isFavorite: {
    type: Boolean,
    default: false
  },
  
  // 是否公开分享
  isShared: {
    type: Boolean,
    default: false
  },
  shareToken: String
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 复合索引
conversationSchema.index({ userId: 1, createdAt: -1 });
conversationSchema.index({ userId: 1, status: 1 });
conversationSchema.index({ userId: 1, isFavorite: 1 });
conversationSchema.index({ 'messages.timestamp': -1 });

// 虚拟字段
conversationSchema.virtual('messageCount').get(function() {
  return this.messages.length;
});

conversationSchema.virtual('lastMessage').get(function() {
  return this.messages.length > 0 ? this.messages[this.messages.length - 1] : null;
});

conversationSchema.virtual('duration').get(function() {
  if (this.messages.length < 2) return 0;
  const firstMessage = this.messages[0];
  const lastMessage = this.messages[this.messages.length - 1];
  return Math.round((lastMessage.timestamp - firstMessage.timestamp) / (1000 * 60)); // 分钟
});

// 中间件：更新时间戳和统计
conversationSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  this.lastActiveAt = Date.now();
  
  // 更新统计信息
  this.stats.messageCount = this.messages.length;
  this.stats.userMessageCount = this.messages.filter(m => m.type === 'user').length;
  this.stats.aiMessageCount = this.messages.filter(m => m.type === 'ai').length;
  
  // 计算总tokens
  this.stats.totalTokens = this.messages
    .filter(m => m.aiMetadata && m.aiMetadata.tokens)
    .reduce((total, m) => total + m.aiMetadata.tokens, 0);
  
  // 计算平均响应时间
  const aiMessages = this.messages.filter(m => m.aiMetadata && m.aiMetadata.responseTime);
  if (aiMessages.length > 0) {
    this.stats.averageResponseTime = aiMessages
      .reduce((total, m) => total + m.aiMetadata.responseTime, 0) / aiMessages.length;
  }
  
  next();
});

// 实例方法：添加消息 (匹配前端addMessage方法)
conversationSchema.methods.addMessage = function(messageData) {
  const message = {
    content: messageData.content,
    type: messageData.type,
    timestamp: messageData.timestamp || new Date(),
    ...messageData
  };
  
  this.messages.push(message);
  this.lastActiveAt = new Date();
  
  return this.save();
};

// 实例方法：获取最近的消息
conversationSchema.methods.getRecentMessages = function(limit = 10) {
  return this.messages.slice(-limit);
};

// 实例方法：清空消息 (匹配前端clearCurrentConversation)
conversationSchema.methods.clearMessages = function() {
  this.messages = [];
  return this.save();
};

// 静态方法：获取用户的对话列表 (匹配前端conversations)
conversationSchema.statics.getUserConversations = function(userId, options = {}) {
  const {
    status = null,
    limit = 20,
    skip = 0,
    sortBy = 'updatedAt',
    sortOrder = -1
  } = options;
  
  const query = { userId };
  if (status) query.status = status;
  
  return this.find(query)
    .sort({ [sortBy]: sortOrder })
    .limit(limit)
    .skip(skip)
    .select('-messages') // 列表查询时不返回消息内容
    .lean();
};

// 静态方法：搜索对话
conversationSchema.statics.searchConversations = function(userId, searchTerm) {
  return this.find({
    userId,
    $or: [
      { title: { $regex: searchTerm, $options: 'i' } },
      { 'messages.content': { $regex: searchTerm, $options: 'i' } }
    ]
  }).sort({ updatedAt: -1 });
};

module.exports = mongoose.model('Conversation', conversationSchema);
