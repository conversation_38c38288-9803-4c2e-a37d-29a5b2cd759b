import { createRouter, createWebHistory } from 'vue-router'
import ChatView from '../views/ChatView.vue'
import LoginView from '../views/LoginView.vue'
import { useAuthStore } from '../stores/auth'
import HistoryView from '../views/HistoryView.vue'
import SettingsView from '../views/SettingsView.vue'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: LoginView,
    meta: {
      title: '登录 - 英语学习助手',
      requiresGuest: true
    }
  },
  {
    path: '/',
    name: 'Chat',
    component: ChatView,
    meta: {
      title: '英语对话练习',
      requiresAuth: true
    }
  },
  {
    path: '/history',
    name: 'History',
    component: HistoryView,
    meta: {
      title: '对话历史',
      requiresAuth: true
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: SettingsView,
    meta: {
      title: '设置',
      requiresAuth: true
    }
  }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title || '英语对话练习'

  const authStore = useAuthStore()

  // 初始化认证状态
  if (!authStore.user && localStorage.getItem('accessToken')) {
    authStore.initializeAuth()

    // 验证token有效性
    const isValid = await authStore.verifyToken()
    if (!isValid) {
      authStore.clearAuth()
    }
  }

  // 检查认证要求
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    // 需要认证但未登录，跳转到登录页
    next({ name: 'Login', query: { redirect: to.fullPath } })
  } else if (to.meta.requiresGuest && authStore.isAuthenticated) {
    // 需要游客状态但已登录，跳转到首页
    next({ name: 'Chat' })
  } else {
    next()
  }
})

export default router
