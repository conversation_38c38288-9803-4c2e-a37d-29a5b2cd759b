<template>
  <div v-if="hasError" class="error-boundary">
    <div class="error-container">
      <div class="error-icon">⚠️</div>
      <h2 class="error-title">出现了一些问题</h2>
      <p class="error-message">{{ errorMessage }}</p>
      
      <div class="error-actions">
        <button @click="retry" class="retry-btn">
          🔄 重试
        </button>
        <button @click="goHome" class="home-btn">
          🏠 返回首页
        </button>
      </div>
      
      <details v-if="errorDetails" class="error-details">
        <summary>错误详情</summary>
        <pre class="error-stack">{{ errorDetails }}</pre>
      </details>
    </div>
  </div>
  <slot v-else />
</template>

<script>
import { ref, onErrorCaptured } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'ErrorBoundary',
  setup() {
    const router = useRouter()
    const hasError = ref(false)
    const errorMessage = ref('')
    const errorDetails = ref('')
    
    onErrorCaptured((error, instance, info) => {
      console.error('Error caught by boundary:', error)
      console.error('Component instance:', instance)
      console.error('Error info:', info)
      
      hasError.value = true
      errorMessage.value = error.message || '发生了未知错误'
      errorDetails.value = error.stack || error.toString()
      
      // 阻止错误继续传播
      return false
    })
    
    const retry = () => {
      hasError.value = false
      errorMessage.value = ''
      errorDetails.value = ''
      
      // 重新加载当前路由
      router.go(0)
    }
    
    const goHome = () => {
      hasError.value = false
      errorMessage.value = ''
      errorDetails.value = ''
      
      router.push('/')
    }
    
    return {
      hasError,
      errorMessage,
      errorDetails,
      retry,
      goHome
    }
  }
}
</script>

<style scoped>
.error-boundary {
  min-height: calc(100vh - 64px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: var(--background-color);
}

.error-container {
  max-width: 500px;
  text-align: center;
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: var(--shadow-lg);
}

.error-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.error-title {
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.error-message {
  color: var(--text-secondary);
  margin-bottom: 2rem;
  line-height: 1.5;
}

.error-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 2rem;
}

.retry-btn,
.home-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-btn {
  background-color: var(--primary-color);
  color: white;
}

.retry-btn:hover {
  background-color: var(--primary-hover);
}

.home-btn {
  background-color: var(--background-color);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.home-btn:hover {
  background-color: var(--border-color);
}

.error-details {
  text-align: left;
  margin-top: 1rem;
}

.error-details summary {
  cursor: pointer;
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.error-details summary:hover {
  color: var(--text-primary);
}

.error-stack {
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  padding: 1rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .error-boundary {
    padding: 1rem;
  }
  
  .error-container {
    padding: 1.5rem;
  }
  
  .error-icon {
    font-size: 3rem;
  }
  
  .error-title {
    font-size: 1.25rem;
  }
  
  .error-actions {
    flex-direction: column;
  }
  
  .retry-btn,
  .home-btn {
    width: 100%;
  }
}
</style>
