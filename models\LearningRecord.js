const mongoose = require('mongoose');

// 学习记录Schema
const learningRecordSchema = new mongoose.Schema({
  // 关联用户
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  
  // 学习类型
  type: {
    type: String,
    required: true,
    enum: ['conversation', 'word', 'grammar', 'reading', 'listening', 'writing'],
    index: true
  },
  
  // 关联内容
  contentId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    index: true
  },
  
  contentType: {
    type: String,
    required: true,
    enum: ['Conversation', 'Word', 'Grammar', 'Reading', 'Exercise']
  },
  
  // 学习会话信息
  sessionId: {
    type: String,
    required: true,
    index: true
  },
  
  // 学习结果
  result: {
    // 基本分数
    score: {
      type: Number,
      min: 0,
      max: 100
    },
    
    // 正确率
    accuracy: {
      type: Number,
      min: 0,
      max: 1
    },
    
    // 完成状态
    status: {
      type: String,
      enum: ['completed', 'partial', 'failed', 'skipped'],
      default: 'completed'
    },
    
    // 用时(秒)
    timeSpent: {
      type: Number,
      min: 0
    },
    
    // 尝试次数
    attempts: {
      type: Number,
      default: 1,
      min: 1
    },
    
    // 详细结果
    details: {
      totalQuestions: Number,
      correctAnswers: Number,
      wrongAnswers: Number,
      skippedQuestions: Number,
      hints: Number, // 使用提示次数
      
      // 错误分析
      mistakes: [{
        question: String,
        userAnswer: String,
        correctAnswer: String,
        explanation: String
      }],
      
      // 强项和弱项
      strengths: [String],
      weaknesses: [String]
    }
  },
  
  // 学习反馈
  feedback: {
    difficulty: {
      type: Number,
      min: 1,
      max: 5
    },
    satisfaction: {
      type: Number,
      min: 1,
      max: 5
    },
    comment: {
      type: String,
      maxlength: [500, '反馈不能超过500个字符']
    },
    isHelpful: Boolean,
    suggestions: [String]
  },
  
  // 学习环境
  environment: {
    device: {
      type: String,
      enum: ['desktop', 'mobile', 'tablet']
    },
    browser: String,
    platform: String,
    studyMode: {
      type: String,
      enum: ['focused', 'casual', 'review']
    }
  },
  
  // 时间信息
  startTime: {
    type: Date,
    required: true
  },
  
  endTime: {
    type: Date,
    required: true
  },
  
  studyDate: {
    type: Date,
    required: true,
    index: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 复合索引
learningRecordSchema.index({ userId: 1, type: 1, studyDate: -1 });
learningRecordSchema.index({ userId: 1, contentId: 1, contentType: 1 });
learningRecordSchema.index({ sessionId: 1, startTime: 1 });

// 虚拟字段
learningRecordSchema.virtual('duration').get(function() {
  return this.endTime - this.startTime; // 毫秒
});

learningRecordSchema.virtual('durationMinutes').get(function() {
  return Math.round((this.endTime - this.startTime) / (1000 * 60));
});

// 静态方法：获取用户学习统计
learningRecordSchema.statics.getUserStats = function(userId, options = {}) {
  const { startDate, endDate, type } = options;
  
  const matchStage = { userId: new mongoose.Types.ObjectId(userId) };
  if (startDate && endDate) {
    matchStage.studyDate = { $gte: startDate, $lte: endDate };
  }
  if (type) matchStage.type = type;
  
  return this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalSessions: { $sum: 1 },
        totalTime: { $sum: '$result.timeSpent' },
        averageScore: { $avg: '$result.score' },
        averageAccuracy: { $avg: '$result.accuracy' },
        completedSessions: {
          $sum: { $cond: [{ $eq: ['$result.status', 'completed'] }, 1, 0] }
        },
        totalQuestions: { $sum: '$result.details.totalQuestions' },
        totalCorrect: { $sum: '$result.details.correctAnswers' }
      }
    }
  ]);
};

// 静态方法：获取学习进度
learningRecordSchema.statics.getLearningProgress = function(userId, contentType) {
  return this.aggregate([
    {
      $match: {
        userId: new mongoose.Types.ObjectId(userId),
        contentType: contentType
      }
    },
    {
      $group: {
        _id: '$contentId',
        lastStudied: { $max: '$studyDate' },
        totalSessions: { $sum: 1 },
        bestScore: { $max: '$result.score' },
        averageScore: { $avg: '$result.score' },
        totalTime: { $sum: '$result.timeSpent' }
      }
    },
    { $sort: { lastStudied: -1 } }
  ]);
};

// 静态方法：获取学习趋势
learningRecordSchema.statics.getLearningTrend = function(userId, days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return this.aggregate([
    {
      $match: {
        userId: new mongoose.Types.ObjectId(userId),
        studyDate: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: {
          year: { $year: '$studyDate' },
          month: { $month: '$studyDate' },
          day: { $dayOfMonth: '$studyDate' }
        },
        sessionsCount: { $sum: 1 },
        totalTime: { $sum: '$result.timeSpent' },
        averageScore: { $avg: '$result.score' }
      }
    },
    { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
  ]);
};

// 静态方法：获取弱项分析
learningRecordSchema.statics.getWeaknessAnalysis = function(userId, limit = 10) {
  return this.aggregate([
    { $match: { userId: new mongoose.Types.ObjectId(userId) } },
    { $unwind: '$result.details.mistakes' },
    {
      $group: {
        _id: '$result.details.mistakes.question',
        errorCount: { $sum: 1 },
        lastError: { $max: '$studyDate' },
        examples: { $push: '$result.details.mistakes' }
      }
    },
    { $sort: { errorCount: -1 } },
    { $limit: limit }
  ]);
};

module.exports = mongoose.model('LearningRecord', learningRecordSchema);
