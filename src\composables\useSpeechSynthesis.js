import { ref, onUnmounted } from 'vue'
import { useSettingsStore } from '../stores/settings'

export function useSpeechSynthesis() {
  const settingsStore = useSettingsStore()
  
  const isSpeaking = ref(false)
  const isPaused = ref(false)
  const isSupported = ref(false)
  const voices = ref([])
  const currentUtterance = ref(null)
  
  // 检查浏览器支持
  const checkSupport = () => {
    isSupported.value = 'speechSynthesis' in window
    return isSupported.value
  }
  
  // 获取可用的语音
  const loadVoices = () => {
    if (!isSupported.value) return
    
    const availableVoices = speechSynthesis.getVoices()
    voices.value = availableVoices.map(voice => ({
      name: voice.name,
      lang: voice.lang,
      localService: voice.localService,
      default: voice.default,
      voiceURI: voice.voiceURI
    }))
  }
  
  // 获取指定语言的语音
  const getVoicesForLanguage = (language) => {
    return voices.value.filter(voice => voice.lang.startsWith(language))
  }
  
  // 获取默认语音
  const getDefaultVoice = (language = 'en') => {
    const languageVoices = getVoicesForLanguage(language)
    return languageVoices.find(voice => voice.default) || languageVoices[0] || voices.value[0]
  }
  
  // 朗读文本
  const speak = (text, options = {}) => {
    if (!isSupported.value) {
      console.warn('当前浏览器不支持语音合成')
      return Promise.reject(new Error('不支持语音合成'))
    }
    
    if (!text || text.trim() === '') {
      return Promise.reject(new Error('文本内容为空'))
    }
    
    // 停止当前朗读
    stop()
    
    return new Promise((resolve, reject) => {
      const utterance = new SpeechSynthesisUtterance(text)
      currentUtterance.value = utterance
      
      // 设置语音参数
      const voiceSettings = settingsStore.voiceSettings
      utterance.rate = options.rate ?? voiceSettings.rate ?? 1
      utterance.pitch = options.pitch ?? voiceSettings.pitch ?? 1
      utterance.volume = options.volume ?? voiceSettings.volume ?? 1
      
      // 选择语音
      if (options.voice) {
        const selectedVoice = voices.value.find(v => v.name === options.voice)
        if (selectedVoice) {
          utterance.voice = speechSynthesis.getVoices().find(v => v.name === selectedVoice.name)
        }
      } else if (voiceSettings.voice) {
        const savedVoice = voices.value.find(v => v.name === voiceSettings.voice)
        if (savedVoice) {
          utterance.voice = speechSynthesis.getVoices().find(v => v.name === savedVoice.name)
        }
      } else {
        // 使用默认英语语音
        const defaultVoice = getDefaultVoice('en')
        if (defaultVoice) {
          utterance.voice = speechSynthesis.getVoices().find(v => v.name === defaultVoice.name)
        }
      }
      
      // 事件监听
      utterance.onstart = () => {
        isSpeaking.value = true
        isPaused.value = false
      }
      
      utterance.onend = () => {
        isSpeaking.value = false
        isPaused.value = false
        currentUtterance.value = null
        resolve()
      }
      
      utterance.onerror = (event) => {
        isSpeaking.value = false
        isPaused.value = false
        currentUtterance.value = null
        console.error('语音合成错误:', event.error)
        reject(new Error(`语音合成错误: ${event.error}`))
      }
      
      utterance.onpause = () => {
        isPaused.value = true
      }
      
      utterance.onresume = () => {
        isPaused.value = false
      }
      
      // 开始朗读
      try {
        speechSynthesis.speak(utterance)
      } catch (error) {
        reject(error)
      }
    })
  }
  
  // 停止朗读
  const stop = () => {
    if (isSupported.value && speechSynthesis.speaking) {
      speechSynthesis.cancel()
      isSpeaking.value = false
      isPaused.value = false
      currentUtterance.value = null
    }
  }
  
  // 暂停朗读
  const pause = () => {
    if (isSupported.value && speechSynthesis.speaking && !speechSynthesis.paused) {
      speechSynthesis.pause()
      isPaused.value = true
    }
  }
  
  // 恢复朗读
  const resume = () => {
    if (isSupported.value && speechSynthesis.paused) {
      speechSynthesis.resume()
      isPaused.value = false
    }
  }
  
  // 切换暂停/恢复
  const togglePause = () => {
    if (isPaused.value) {
      resume()
    } else {
      pause()
    }
  }
  
  // 设置语音参数
  const setVoiceSettings = (settings) => {
    settingsStore.updateVoiceSettings(settings)
  }
  
  // 测试语音
  const testVoice = (voiceName, text = 'Hello, this is a test of the speech synthesis.') => {
    return speak(text, { voice: voiceName })
  }
  
  // 初始化
  const init = () => {
    checkSupport()
    
    if (isSupported.value) {
      // 加载语音列表
      loadVoices()
      
      // 监听语音列表变化（某些浏览器需要异步加载）
      if (speechSynthesis.onvoiceschanged !== undefined) {
        speechSynthesis.onvoiceschanged = loadVoices
      }
    }
  }
  
  // 组件卸载时清理
  onUnmounted(() => {
    stop()
    if (isSupported.value && speechSynthesis.onvoiceschanged) {
      speechSynthesis.onvoiceschanged = null
    }
  })
  
  // 初始化
  init()
  
  return {
    // 状态
    isSpeaking,
    isPaused,
    isSupported,
    voices,
    currentUtterance,
    
    // 方法
    speak,
    stop,
    pause,
    resume,
    togglePause,
    setVoiceSettings,
    testVoice,
    getVoicesForLanguage,
    getDefaultVoice,
    loadVoices
  }
}

// 获取推荐的英语语音
export function getRecommendedEnglishVoices() {
  return [
    'Google US English',
    'Google UK English Female',
    'Google UK English Male',
    'Microsoft Zira Desktop - English (United States)',
    'Microsoft David Desktop - English (United States)',
    'Microsoft Hazel Desktop - English (Great Britain)',
    'Microsoft George Desktop - English (Great Britain)',
    'Alex',
    'Samantha',
    'Victoria',
    'Karen',
    'Daniel'
  ]
}

// 语音质量评估
export function assessVoiceQuality(voice) {
  const quality = {
    score: 0,
    factors: []
  }
  
  // Google 语音通常质量较高
  if (voice.name.includes('Google')) {
    quality.score += 30
    quality.factors.push('Google 高质量语音')
  }
  
  // 本地语音通常更稳定
  if (voice.localService) {
    quality.score += 20
    quality.factors.push('本地语音服务')
  }
  
  // 默认语音通常经过优化
  if (voice.default) {
    quality.score += 15
    quality.factors.push('系统默认语音')
  }
  
  // 特定的高质量语音
  const highQualityVoices = ['Alex', 'Samantha', 'Victoria', 'Karen', 'Daniel']
  if (highQualityVoices.some(name => voice.name.includes(name))) {
    quality.score += 25
    quality.factors.push('高质量语音引擎')
  }
  
  // Microsoft 语音
  if (voice.name.includes('Microsoft')) {
    quality.score += 15
    quality.factors.push('Microsoft 语音')
  }
  
  return {
    ...quality,
    level: quality.score >= 50 ? 'high' : quality.score >= 30 ? 'medium' : 'low'
  }
}
