const mongoose = require('mongoose');

// 单词Schema
const wordSchema = new mongoose.Schema({
  // 基本信息
  word: {
    type: String,
    required: [true, '单词是必需的'],
    unique: true,
    lowercase: true,
    trim: true,
    index: true
  },
  
  // 发音信息
  pronunciation: {
    phonetic: String, // 音标
    audio: {
      us: String, // 美式发音音频URL
      uk: String  // 英式发音音频URL
    }
  },
  
  // 词性和释义
  definitions: [{
    partOfSpeech: {
      type: String,
      enum: ['noun', 'verb', 'adjective', 'adverb', 'preposition', 'conjunction', 'interjection', 'pronoun'],
      required: true
    },
    meaning: {
      type: String,
      required: true,
      maxlength: [500, '释义不能超过500个字符']
    },
    chineseMeaning: {
      type: String,
      maxlength: [200, '中文释义不能超过200个字符']
    },
    examples: [{
      sentence: {
        type: String,
        required: true,
        maxlength: [300, '例句不能超过300个字符']
      },
      translation: {
        type: String,
        maxlength: [300, '翻译不能超过300个字符']
      }
    }]
  }],
  
  // 词汇等级
  level: {
    type: String,
    enum: ['elementary', 'intermediate', 'advanced', 'expert'],
    default: 'intermediate',
    index: true
  },
  
  // 词频信息
  frequency: {
    rank: Number, // 词频排名
    score: Number // 词频分数
  },
  
  // 分类标签
  categories: [{
    type: String,
    enum: ['academic', 'business', 'daily', 'travel', 'technology', 'science', 'art', 'sports'],
    index: true
  }],
  
  // 相关词汇
  related: {
    synonyms: [String], // 同义词
    antonyms: [String], // 反义词
    derivatives: [String], // 派生词
    collocations: [String] // 搭配词
  },
  
  // 学习统计
  stats: {
    totalLearners: {
      type: Number,
      default: 0
    },
    averageScore: {
      type: Number,
      default: 0
    },
    difficultyRating: {
      type: Number,
      default: 0,
      min: 0,
      max: 5
    }
  },
  
  // 创建信息
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 索引
wordSchema.index({ word: 'text' });
wordSchema.index({ level: 1, categories: 1 });
wordSchema.index({ 'frequency.rank': 1 });

// 虚拟字段
wordSchema.virtual('definitionCount').get(function() {
  return this.definitions.length;
});

wordSchema.virtual('exampleCount').get(function() {
  return this.definitions.reduce((total, def) => total + def.examples.length, 0);
});

// 静态方法：按等级查找单词
wordSchema.statics.findByLevel = function(level, limit = 50) {
  return this.find({ level, isActive: true })
    .sort({ 'frequency.rank': 1 })
    .limit(limit);
};

// 静态方法：搜索单词
wordSchema.statics.searchWords = function(searchTerm, options = {}) {
  const { level, categories, limit = 20 } = options;
  
  const query = {
    isActive: true,
    $or: [
      { word: { $regex: searchTerm, $options: 'i' } },
      { 'definitions.meaning': { $regex: searchTerm, $options: 'i' } },
      { 'definitions.chineseMeaning': { $regex: searchTerm, $options: 'i' } }
    ]
  };
  
  if (level) query.level = level;
  if (categories && categories.length > 0) query.categories = { $in: categories };
  
  return this.find(query)
    .sort({ 'frequency.rank': 1 })
    .limit(limit);
};

// 静态方法：获取随机单词
wordSchema.statics.getRandomWords = function(count = 10, level = null) {
  const matchStage = { isActive: true };
  if (level) matchStage.level = level;
  
  return this.aggregate([
    { $match: matchStage },
    { $sample: { size: count } }
  ]);
};

module.exports = mongoose.model('Word', wordSchema);
