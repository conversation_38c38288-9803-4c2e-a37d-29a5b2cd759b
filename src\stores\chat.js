import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import { aiAPI, handleApiError } from '../services/api'
import { useAuthStore } from './auth'

export const useChatStore = defineStore('chat', () => {
  // 状态
  const messages = ref([])
  const isLoading = ref(false)
  const currentConversationId = ref(null)
  const conversations = ref([])
  const error = ref(null)
  
  // 计算属性
  const currentMessages = computed(() => {
    if (!currentConversationId.value) return messages.value
    const conversation = conversations.value.find(c => c.id === currentConversationId.value)
    return conversation ? conversation.messages : []
  })
  
  const conversationCount = computed(() => conversations.value.length)
  
  // 方法
  const addMessage = (message) => {
    const newMessage = {
      id: Date.now(),
      content: message.content,
      type: message.type, // 'user' 或 'ai'
      timestamp: new Date(),
      ...message
    }
    
    if (currentConversationId.value) {
      const conversation = conversations.value.find(c => c.id === currentConversationId.value)
      if (conversation) {
        conversation.messages.push(newMessage)
        conversation.updatedAt = new Date()
      }
    } else {
      messages.value.push(newMessage)
    }
  }
  
  const createNewConversation = () => {
    const newConversation = {
      id: Date.now(),
      title: `对话 ${conversations.value.length + 1}`,
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date()
    }
    
    conversations.value.unshift(newConversation)
    currentConversationId.value = newConversation.id
    
    return newConversation.id
  }
  
  const switchConversation = (conversationId) => {
    currentConversationId.value = conversationId
  }
  
  const deleteConversation = (conversationId) => {
    const index = conversations.value.findIndex(c => c.id === conversationId)
    if (index !== -1) {
      conversations.value.splice(index, 1)
      if (currentConversationId.value === conversationId) {
        currentConversationId.value = conversations.value.length > 0 ? conversations.value[0].id : null
      }
    }
  }
  
  const clearCurrentConversation = () => {
    if (currentConversationId.value) {
      const conversation = conversations.value.find(c => c.id === currentConversationId.value)
      if (conversation) {
        conversation.messages = []
      }
    } else {
      messages.value = []
    }
  }
  
  const setLoading = (loading) => {
    isLoading.value = loading
  }

  // 数据持久化
  const saveToStorage = () => {
    try {
      const data = {
        conversations: conversations.value,
        currentConversationId: currentConversationId.value,
        lastSaved: new Date().toISOString()
      }
      localStorage.setItem('chatData', JSON.stringify(data))
    } catch (error) {
      console.error('保存聊天数据失败:', error)
    }
  }

  const loadFromStorage = () => {
    try {
      const saved = localStorage.getItem('chatData')
      if (saved) {
        const data = JSON.parse(saved)
        conversations.value = data.conversations || []
        currentConversationId.value = data.currentConversationId || null
      }
    } catch (error) {
      console.error('加载聊天数据失败:', error)
    }
  }

  // 监听数据变化并自动保存
  const startAutoSave = () => {
    // 监听对话列表变化
    const stopWatchingConversations = watch(
      conversations,
      () => {
        saveToStorage()
      },
      { deep: true }
    )

    // 监听当前对话变化
    const stopWatchingCurrentId = watch(
      currentConversationId,
      () => {
        saveToStorage()
      }
    )

    return () => {
      stopWatchingConversations()
      stopWatchingCurrentId()
    }
  }

  // API调用方法
  // 发送消息到AI
  const sendMessage = async (messageContent, settings = {}) => {
    const authStore = useAuthStore()

    if (!authStore.isAuthenticated) {
      error.value = '请先登录'
      return { success: false, error: '请先登录' }
    }

    isLoading.value = true
    error.value = null

    try {
      const messageData = {
        message: messageContent,
        settings
      }

      // 只有当conversationId是有效的MongoDB ObjectId格式时才发送
      if (currentConversationId.value &&
          typeof currentConversationId.value === 'string' &&
          currentConversationId.value.match(/^[0-9a-fA-F]{24}$/)) {
        messageData.conversationId = currentConversationId.value
        console.log('📤 发送消息到现有对话:', currentConversationId.value)
      } else {
        console.log('📤 发送消息创建新对话 (无有效对话ID)')
        // 清除无效的对话ID
        if (currentConversationId.value) {
          console.log('🗑️ 清除无效的对话ID:', currentConversationId.value)
          currentConversationId.value = null
        }
      }

      const response = await aiAPI.chat(messageData)
      const { conversation, aiResponse } = response.data

      console.log('🔍 API响应数据:', {
        conversationId: conversation.id,
        currentConversationId: currentConversationId.value,
        messagesCount: conversation.messages.length
      })

      // 更新当前对话ID - 总是使用后端返回的有效ID
      if (!currentConversationId.value || currentConversationId.value !== conversation.id) {
        currentConversationId.value = conversation.id
        console.log('📝 设置对话ID:', conversation.id)
      } else {
        console.log('📋 继续使用现有对话ID:', currentConversationId.value)
      }

      // 更新对话列表中的对话
      const existingIndex = conversations.value.findIndex(c => c.id === conversation.id)
      if (existingIndex >= 0) {
        conversations.value[existingIndex] = {
          ...conversations.value[existingIndex],
          ...conversation,
          messages: conversation.messages
        }
      } else {
        // 新对话，添加到列表开头
        conversations.value.unshift({
          id: conversation.id,
          title: conversation.title,
          messages: conversation.messages,
          updatedAt: conversation.updatedAt,
          createdAt: new Date().toISOString()
        })
      }

      // 如果这是当前对话，更新当前显示的消息
      if (currentConversationId.value === conversation.id) {
        messages.value = conversation.messages
        console.log('✅ 更新当前消息显示:', messages.value.length, '条消息')
      } else {
        console.log('⚠️ 对话ID不匹配:', { current: currentConversationId.value, received: conversation.id })
      }

      return { success: true, data: { conversation, aiResponse } }
    } catch (err) {
      const errorInfo = handleApiError(err)
      error.value = errorInfo.message
      return { success: false, error: errorInfo }
    } finally {
      isLoading.value = false
    }
  }

  // 加载对话列表
  const loadConversations = async () => {
    const authStore = useAuthStore()

    if (!authStore.isAuthenticated) {
      return { success: false, error: '请先登录' }
    }

    isLoading.value = true
    error.value = null

    try {
      const response = await aiAPI.getConversations()
      conversations.value = response.data.conversations
      return { success: true, data: response.data }
    } catch (err) {
      const errorInfo = handleApiError(err)
      error.value = errorInfo.message
      return { success: false, error: errorInfo }
    } finally {
      isLoading.value = false
    }
  }

  // 加载单个对话详情
  const loadConversation = async (conversationId) => {
    const authStore = useAuthStore()

    if (!authStore.isAuthenticated) {
      return { success: false, error: '请先登录' }
    }

    isLoading.value = true
    error.value = null

    try {
      const response = await aiAPI.getConversation(conversationId)
      const conversation = response.data.conversation

      // 更新对话列表
      const existingIndex = conversations.value.findIndex(c => c.id === conversationId)
      if (existingIndex >= 0) {
        conversations.value[existingIndex] = conversation
      }

      // 如果是当前对话，更新消息
      if (currentConversationId.value === conversationId) {
        messages.value = conversation.messages
      }

      return { success: true, data: conversation }
    } catch (err) {
      const errorInfo = handleApiError(err)
      error.value = errorInfo.message
      return { success: false, error: errorInfo }
    } finally {
      isLoading.value = false
    }
  }

  // 删除对话（API版本）
  const deleteConversationAPI = async (conversationId) => {
    const authStore = useAuthStore()

    if (!authStore.isAuthenticated) {
      return { success: false, error: '请先登录' }
    }

    isLoading.value = true
    error.value = null

    try {
      await aiAPI.deleteConversation(conversationId)

      // 从本地列表中删除
      const index = conversations.value.findIndex(c => c.id === conversationId)
      if (index !== -1) {
        conversations.value.splice(index, 1)

        // 如果删除的是当前对话，切换到其他对话或清空
        if (currentConversationId.value === conversationId) {
          currentConversationId.value = conversations.value.length > 0 ? conversations.value[0].id : null
          messages.value = []
        }
      }

      return { success: true }
    } catch (err) {
      const errorInfo = handleApiError(err)
      error.value = errorInfo.message
      return { success: false, error: errorInfo }
    } finally {
      isLoading.value = false
    }
  }

  // 清除错误
  const clearError = () => {
    error.value = null
  }

  return {
    // 状态
    messages,
    isLoading,
    currentConversationId,
    conversations,
    error,

    // 计算属性
    currentMessages,
    conversationCount,

    // 本地方法
    addMessage,
    createNewConversation,
    switchConversation,
    deleteConversation,
    clearCurrentConversation,
    setLoading,
    saveToStorage,
    loadFromStorage,
    startAutoSave,

    // API方法
    sendMessage,
    loadConversations,
    loadConversation,
    deleteConversationAPI,
    clearError
  }
})
