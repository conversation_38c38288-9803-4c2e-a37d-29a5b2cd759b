# 依赖
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
logs
*.log

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage/
.nyc_output

# 构建输出
dist/
build/

# 临时文件
.tmp/
temp/

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE 文件
.vscode/
.idea/
*.swp
*.swo
*~

# 上传文件
uploads/
public/uploads/

# 测试文件
test-results/

# 缓存
.cache/
