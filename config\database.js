const mongoose = require('mongoose');
const config = require('./index');

class Database {
  constructor() {
    this.isConnected = false;
    this.connectionAttempts = 0;
    this.maxRetries = 5;
    this.retryDelay = 5000; // 5秒
  }

  // 连接数据库
  async connect() {
    try {
      // 如果已经连接，直接返回
      if (this.isConnected) {
        console.log('📊 数据库已连接');
        return;
      }

      console.log('🔄 正在连接MongoDB数据库...');
      
      // 设置Mongoose配置
      mongoose.set('strictQuery', false);
      
      // 连接选项
      const options = {
        ...config.database.options,
        maxPoolSize: 10, // 连接池最大连接数
        serverSelectionTimeoutMS: 5000, // 服务器选择超时
        socketTimeoutMS: 45000, // Socket超时
        family: 4 // 使用IPv4
      };

      // 连接数据库
      await mongoose.connect(config.database.uri, options);
      
      this.isConnected = true;
      this.connectionAttempts = 0;
      
      console.log('✅ MongoDB数据库连接成功');
      console.log(`📍 数据库地址: ${this.maskConnectionString(config.database.uri)}`);
      
      // 设置连接事件监听
      this.setupEventListeners();
      
    } catch (error) {
      console.error('❌ MongoDB连接失败:', error.message);
      
      this.connectionAttempts++;
      
      if (this.connectionAttempts < this.maxRetries) {
        console.log(`🔄 ${this.retryDelay / 1000}秒后重试连接... (${this.connectionAttempts}/${this.maxRetries})`);
        setTimeout(() => this.connect(), this.retryDelay);
      } else {
        console.error('💥 达到最大重试次数，数据库连接失败');
        process.exit(1);
      }
    }
  }

  // 断开数据库连接
  async disconnect() {
    try {
      if (this.isConnected) {
        await mongoose.disconnect();
        this.isConnected = false;
        console.log('📊 数据库连接已断开');
      }
    } catch (error) {
      console.error('❌ 断开数据库连接时出错:', error.message);
    }
  }

  // 设置事件监听器
  setupEventListeners() {
    const db = mongoose.connection;

    // 连接成功
    db.on('connected', () => {
      console.log('🔗 Mongoose连接已建立');
      this.isConnected = true;
    });

    // 连接错误
    db.on('error', (error) => {
      console.error('❌ Mongoose连接错误:', error);
      this.isConnected = false;
    });

    // 连接断开
    db.on('disconnected', () => {
      console.log('🔌 Mongoose连接已断开');
      this.isConnected = false;
      
      // 如果不是主动断开，尝试重连
      if (this.connectionAttempts < this.maxRetries) {
        console.log('🔄 尝试重新连接数据库...');
        setTimeout(() => this.connect(), this.retryDelay);
      }
    });

    // 应用终止时断开连接
    process.on('SIGINT', async () => {
      console.log('\n🛑 收到SIGINT信号，正在关闭数据库连接...');
      await this.disconnect();
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      console.log('\n🛑 收到SIGTERM信号，正在关闭数据库连接...');
      await this.disconnect();
      process.exit(0);
    });
  }

  // 掩码连接字符串（隐藏敏感信息）
  maskConnectionString(connectionString) {
    return connectionString.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@');
  }

  // 获取连接状态
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      readyState: mongoose.connection.readyState,
      host: mongoose.connection.host,
      port: mongoose.connection.port,
      name: mongoose.connection.name
    };
  }

  // 健康检查
  async healthCheck() {
    try {
      if (!this.isConnected) {
        return { status: 'disconnected', message: '数据库未连接' };
      }

      // 执行简单查询测试连接
      await mongoose.connection.db.admin().ping();
      
      return {
        status: 'healthy',
        message: '数据库连接正常',
        details: this.getConnectionStatus()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        message: '数据库连接异常',
        error: error.message
      };
    }
  }

  // 获取数据库统计信息
  async getStats() {
    try {
      if (!this.isConnected) {
        throw new Error('数据库未连接');
      }

      const stats = await mongoose.connection.db.stats();
      
      return {
        collections: stats.collections,
        dataSize: stats.dataSize,
        storageSize: stats.storageSize,
        indexes: stats.indexes,
        indexSize: stats.indexSize,
        objects: stats.objects
      };
    } catch (error) {
      console.error('获取数据库统计信息失败:', error);
      throw error;
    }
  }

  // 清理数据库（仅用于测试环境）
  async clearDatabase() {
    if (config.nodeEnv === 'production') {
      throw new Error('生产环境不允许清理数据库');
    }

    try {
      const collections = await mongoose.connection.db.listCollections().toArray();
      
      for (const collection of collections) {
        await mongoose.connection.db.collection(collection.name).deleteMany({});
      }
      
      console.log('🧹 测试数据库已清理');
    } catch (error) {
      console.error('清理数据库失败:', error);
      throw error;
    }
  }

  // 创建索引
  async createIndexes() {
    try {
      console.log('🔍 正在创建数据库索引...');
      
      // 这里可以添加自定义索引创建逻辑
      // 大部分索引已在模型中定义
      
      console.log('✅ 数据库索引创建完成');
    } catch (error) {
      console.error('❌ 创建数据库索引失败:', error);
      throw error;
    }
  }
}

// 创建数据库实例
const database = new Database();

// 导出数据库实例和连接方法
module.exports = {
  database,
  connect: () => database.connect(),
  disconnect: () => database.disconnect(),
  healthCheck: () => database.healthCheck(),
  getStats: () => database.getStats(),
  clearDatabase: () => database.clearDatabase(),
  createIndexes: () => database.createIndexes(),
  getConnectionStatus: () => database.getConnectionStatus()
};
