const axios = require('axios');

class SiliconFlowService {
  constructor() {
    this.apiKey = process.env.SILICON_FLOW_API_KEY;
    this.baseURL = 'https://api.siliconflow.cn/v1';
    this.model = 'Qwen/Qwen2.5-7B-Instruct'; // 推荐的英语学习模型
    
    if (!this.apiKey) {
      console.warn('⚠️ Silicon Flow API密钥未配置');
      this.available = false;
      return;
    }

    this.client = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });

    this.available = true;
    console.log('✅ Silicon Flow AI服务初始化成功');
  }

  // 检查服务是否可用
  isAvailable() {
    return this.available;
  }

  // 生成对话回复
  async generateChatResponse(message, options = {}) {
    if (!this.isAvailable()) {
      throw new Error('Silicon Flow服务不可用');
    }

    try {
      const {
        systemPrompt = '你是一个专业的英语对话练习助手。请用英语与用户对话，帮助用户提高英语口语水平。根据用户的英语水平调整你的回复难度，给出自然、地道的英语表达，并在适当时候提供学习建议。',
        conversationHistory = [],
        userLevel = 'intermediate',
        maxTokens = 1000
      } = options;

      // 构建消息历史
      const messages = [
        {
          role: 'system',
          content: this.buildSystemPrompt(systemPrompt, userLevel)
        }
      ];

      // 添加对话历史（最近10条）
      const recentHistory = conversationHistory.slice(-10);
      recentHistory.forEach(msg => {
        messages.push({
          role: msg.type === 'user' ? 'user' : 'assistant',
          content: msg.content
        });
      });

      // 添加当前用户消息
      messages.push({
        role: 'user',
        content: message
      });

      const startTime = Date.now();
      
      const response = await this.client.post('/chat/completions', {
        model: this.model,
        messages: messages,
        max_tokens: maxTokens,
        temperature: 0.7,
        top_p: 0.9,
        stream: false
      });

      const responseTime = Date.now() - startTime;
      const aiMessage = response.data.choices[0].message.content;

      return {
        content: aiMessage.trim(),
        metadata: {
          model: this.model,
          responseTime,
          tokens: response.data.usage?.total_tokens || this.estimateTokens(message + aiMessage),
          confidence: 0.95,
          provider: 'silicon-flow'
        }
      };

    } catch (error) {
      console.error('Silicon Flow对话生成失败:', error.response?.data || error.message);
      throw new Error(`Silicon Flow对话生成失败: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  // 语法检查和纠正
  async checkGrammar(text, options = {}) {
    if (!this.isAvailable()) {
      throw new Error('Silicon Flow服务不可用');
    }

    try {
      const {
        language = 'english',
        includeExplanation = true
      } = options;

      const prompt = `请作为英语语法专家，检查以下${language}文本的语法错误并提供纠正建议：

文本: "${text}"

请按以下格式回复：
1. 是否有语法错误：[是/否]
2. 纠正后的文本：[如果有错误，提供纠正版本]
3. 错误解释：[如果有错误且需要解释，说明错误类型和原因]
4. 建议：[提供改进建议]

请用中文解释，但纠正后的文本保持原语言。`;

      const response = await this.client.post('/chat/completions', {
        model: this.model,
        messages: [
          {
            role: 'system',
            content: '你是一个专业的英语语法检查助手，能够准确识别语法错误并提供详细的纠正建议。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 800,
        temperature: 0.3
      });

      return {
        originalText: text,
        analysis: response.data.choices[0].message.content.trim(),
        timestamp: new Date()
      };

    } catch (error) {
      console.error('Silicon Flow语法检查失败:', error.response?.data || error.message);
      throw new Error(`语法检查失败: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  // 作文批改
  async reviewEssay(essay, options = {}) {
    if (!this.isAvailable()) {
      throw new Error('Silicon Flow服务不可用');
    }

    try {
      const {
        topic = '',
        level = 'intermediate',
        criteria = ['grammar', 'vocabulary', 'structure', 'content']
      } = options;

      const prompt = `请对以下英语作文进行详细批改和评分：

作文题目：${topic}
学生水平：${level}
作文内容：
"${essay}"

请按以下标准进行评价：
1. 语法准确性 (Grammar)
2. 词汇使用 (Vocabulary) 
3. 文章结构 (Structure)
4. 内容质量 (Content)

请提供：
1. 总体评分（1-10分）
2. 各项评分和具体建议
3. 优点总结
4. 需要改进的地方
5. 修改建议

请用中文进行评价和建议。`;

      const response = await this.client.post('/chat/completions', {
        model: this.model,
        messages: [
          {
            role: 'system',
            content: '你是一个专业的英语写作指导老师，能够对英语作文进行全面、客观的评价和指导。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 1500,
        temperature: 0.4
      });

      return {
        originalEssay: essay,
        review: response.data.choices[0].message.content.trim(),
        topic,
        level,
        timestamp: new Date()
      };

    } catch (error) {
      console.error('Silicon Flow作文批改失败:', error.response?.data || error.message);
      throw new Error(`作文批改失败: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  // 构建系统提示词
  buildSystemPrompt(basePrompt, userLevel) {
    const levelInstructions = {
      beginner: '请使用简单的词汇和句型，语速较慢，多给予鼓励。避免使用复杂的语法结构。',
      intermediate: '请使用中等难度的词汇，适当纠正语法错误，可以介绍一些新的表达方式。',
      advanced: '请使用丰富的词汇和复杂句型，深入讨论话题，可以使用习语和高级表达。'
    };

    return `${basePrompt}

用户英语水平：${userLevel}
指导原则：${levelInstructions[userLevel] || levelInstructions.intermediate}

请注意：
- 保持对话自然流畅
- 根据用户水平调整语言难度
- 在适当时候提供学习建议
- 鼓励用户继续练习`;
  }

  // 估算token数量
  estimateTokens(text) {
    // 简单估算：英文约4个字符=1个token，中文约1.5个字符=1个token
    const englishChars = (text.match(/[a-zA-Z\s]/g) || []).length;
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const otherChars = text.length - englishChars - chineseChars;
    
    return Math.ceil(englishChars / 4 + chineseChars / 1.5 + otherChars / 3);
  }

  // 获取服务状态
  getStatus() {
    return {
      available: this.isAvailable(),
      model: this.isAvailable() ? this.model : null,
      provider: 'silicon-flow',
      timestamp: new Date()
    };
  }
}

module.exports = new SiliconFlowService();
