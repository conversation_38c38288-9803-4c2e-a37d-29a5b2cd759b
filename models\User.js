const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  // 基本用户信息
  username: {
    type: String,
    required: [true, '用户名是必需的'],
    unique: true,
    trim: true,
    minlength: [3, '用户名至少需要3个字符'],
    maxlength: [30, '用户名不能超过30个字符']
  },
  email: {
    type: String,
    required: [true, '邮箱是必需的'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, '请输入有效的邮箱地址']
  },
  password: {
    type: String,
    required: [true, '密码是必需的'],
    minlength: [6, '密码至少需要6个字符'],
    select: false // 默认查询时不返回密码
  },
  
  // 用户资料
  profile: {
    firstName: {
      type: String,
      trim: true,
      maxlength: [50, '名字不能超过50个字符']
    },
    lastName: {
      type: String,
      trim: true,
      maxlength: [50, '姓氏不能超过50个字符']
    },
    avatar: {
      type: String,
      default: null
    },
    bio: {
      type: String,
      maxlength: [500, '个人简介不能超过500个字符']
    },
    dateOfBirth: Date,
    location: {
      type: String,
      maxlength: [100, '位置不能超过100个字符']
    }
  },
  
  // 学习偏好设置 (匹配前端settings store)
  settings: {
    // 界面设置
    theme: {
      type: String,
      enum: ['light', 'dark'],
      default: 'light'
    },
    language: {
      type: String,
      default: 'zh-CN'
    },
    
    // 语音设置
    voiceSettings: {
      enabled: {
        type: Boolean,
        default: true
      },
      autoPlay: {
        type: Boolean,
        default: true
      },
      voice: {
        type: String,
        default: null
      },
      rate: {
        type: Number,
        default: 1,
        min: 0.1,
        max: 3
      },
      pitch: {
        type: Number,
        default: 1,
        min: 0,
        max: 2
      },
      volume: {
        type: Number,
        default: 1,
        min: 0,
        max: 1
      }
    },
    
    // 语音识别设置
    speechRecognition: {
      enabled: {
        type: Boolean,
        default: true
      },
      language: {
        type: String,
        default: 'en-US'
      },
      continuous: {
        type: Boolean,
        default: false
      },
      interimResults: {
        type: Boolean,
        default: true
      }
    },
    
    // AI设置
    aiSettings: {
      model: {
        type: String,
        default: 'gpt-3.5-turbo'
      },
      temperature: {
        type: Number,
        default: 0.7,
        min: 0,
        max: 2
      },
      maxTokens: {
        type: Number,
        default: 1000,
        min: 1,
        max: 4000
      },
      systemPrompt: {
        type: String,
        default: '你是一个英语对话练习助手，请用英语与用户对话，帮助用户提高英语口语水平。'
      }
    }
  },
  
  // 学习统计
  learningStats: {
    totalConversations: {
      type: Number,
      default: 0
    },
    totalMessages: {
      type: Number,
      default: 0
    },
    studyTimeMinutes: {
      type: Number,
      default: 0
    },
    lastActiveDate: {
      type: Date,
      default: Date.now
    },
    streakDays: {
      type: Number,
      default: 0
    },
    level: {
      type: String,
      enum: ['beginner', 'intermediate', 'advanced'],
      default: 'beginner'
    }
  },
  
  // 账户状态
  isActive: {
    type: Boolean,
    default: true
  },
  isEmailVerified: {
    type: Boolean,
    default: false
  },
  emailVerificationToken: String,
  passwordResetToken: String,
  passwordResetExpires: Date,
  
  // 时间戳
  lastLoginAt: Date,
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 索引
userSchema.index({ email: 1 });
userSchema.index({ username: 1 });
userSchema.index({ 'learningStats.lastActiveDate': -1 });

// 虚拟字段
userSchema.virtual('fullName').get(function() {
  if (this.profile.firstName && this.profile.lastName) {
    return `${this.profile.firstName} ${this.profile.lastName}`;
  }
  return this.username;
});

// 中间件：保存前加密密码
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// 中间件：更新时间戳
userSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// 实例方法：验证密码
userSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

// 实例方法：生成JWT令牌 (稍后在auth中实现)
userSchema.methods.generateAuthToken = function() {
  // 这个方法将在auth服务中实现
  return null;
};

// 实例方法：更新学习统计
userSchema.methods.updateLearningStats = function(statsUpdate) {
  Object.assign(this.learningStats, statsUpdate);
  this.learningStats.lastActiveDate = new Date();
  return this.save();
};

// 静态方法：查找活跃用户
userSchema.statics.findActiveUsers = function() {
  return this.find({ isActive: true });
};

module.exports = mongoose.model('User', userSchema);
