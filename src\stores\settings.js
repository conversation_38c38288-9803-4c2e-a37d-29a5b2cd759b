import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useSettingsStore = defineStore('settings', () => {
  // 状态
  const theme = ref('light') // 'light' 或 'dark'
  const language = ref('zh-CN') // 界面语言
  const voiceSettings = ref({
    enabled: true,
    autoPlay: true,
    voice: null, // 选择的语音
    rate: 1, // 语速
    pitch: 1, // 音调
    volume: 1 // 音量
  })
  
  const speechRecognition = ref({
    enabled: true,
    language: 'en-US', // 识别语言
    continuous: false,
    interimResults: true
  })
  
  const aiSettings = ref({
    model: 'gpt-3.5-turbo',
    temperature: 0.7,
    maxTokens: 1000,
    systemPrompt: '你是一个英语对话练习助手，请用英语与用户对话，帮助用户提高英语口语水平。'
  })
  
  // 方法
  const updateTheme = (newTheme) => {
    theme.value = newTheme
    document.documentElement.setAttribute('data-theme', newTheme)
    localStorage.setItem('theme', newTheme)
  }
  
  const updateLanguage = (newLanguage) => {
    language.value = newLanguage
    localStorage.setItem('language', newLanguage)
  }
  
  const updateVoiceSettings = (settings) => {
    voiceSettings.value = { ...voiceSettings.value, ...settings }
    localStorage.setItem('voiceSettings', JSON.stringify(voiceSettings.value))
  }
  
  const updateSpeechRecognition = (settings) => {
    speechRecognition.value = { ...speechRecognition.value, ...settings }
    localStorage.setItem('speechRecognition', JSON.stringify(speechRecognition.value))
  }
  
  const updateAiSettings = (settings) => {
    aiSettings.value = { ...aiSettings.value, ...settings }
    localStorage.setItem('aiSettings', JSON.stringify(aiSettings.value))
  }
  
  const loadSettings = () => {
    // 从 localStorage 加载设置
    const savedTheme = localStorage.getItem('theme')
    if (savedTheme) {
      updateTheme(savedTheme)
    }
    
    const savedLanguage = localStorage.getItem('language')
    if (savedLanguage) {
      language.value = savedLanguage
    }
    
    const savedVoiceSettings = localStorage.getItem('voiceSettings')
    if (savedVoiceSettings) {
      voiceSettings.value = JSON.parse(savedVoiceSettings)
    }
    
    const savedSpeechRecognition = localStorage.getItem('speechRecognition')
    if (savedSpeechRecognition) {
      speechRecognition.value = JSON.parse(savedSpeechRecognition)
    }
    
    const savedAiSettings = localStorage.getItem('aiSettings')
    if (savedAiSettings) {
      aiSettings.value = JSON.parse(savedAiSettings)
    }
  }
  
  const resetSettings = () => {
    updateTheme('light')
    updateLanguage('zh-CN')
    updateVoiceSettings({
      enabled: true,
      autoPlay: true,
      voice: null,
      rate: 1,
      pitch: 1,
      volume: 1
    })
    updateSpeechRecognition({
      enabled: true,
      language: 'en-US',
      continuous: false,
      interimResults: true
    })
    updateAiSettings({
      model: 'gpt-3.5-turbo',
      temperature: 0.7,
      maxTokens: 1000,
      systemPrompt: '你是一个英语对话练习助手，请用英语与用户对话，帮助用户提高英语口语水平。'
    })
  }
  
  return {
    // 状态
    theme,
    language,
    voiceSettings,
    speechRecognition,
    aiSettings,
    
    // 方法
    updateTheme,
    updateLanguage,
    updateVoiceSettings,
    updateSpeechRecognition,
    updateAiSettings,
    loadSettings,
    resetSettings
  }
})
