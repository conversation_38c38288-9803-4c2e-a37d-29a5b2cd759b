# 📖 英语学习助手使用说明

## 🚀 快速开始

### 启动应用
1. **启动后端服务**
   ```bash
   cd englishbackend
   npm start
   ```
   - 服务地址：`http://localhost:3000`
   - 健康检查：`http://localhost:3000/health`

2. **启动前端应用**
   ```bash
   cd englishfrontend
   npm run serve
   ```
   - 应用地址：`http://localhost:8080`

3. **首次使用**
   - 注册新账号或登录现有账号
   - 登录后即可开始使用所有功能

## 💬 对话功能

### 基本对话
- **文字输入**：在输入框中输入英语消息
- **发送消息**：按 `Enter` 键或点击 📤 发送按钮
- **AI回复**：AI会用英语回复，帮助你练习口语
- **对话连续性**：AI会记住之前的对话内容，保持上下文连贯

### 语音功能
- **🎤 语音输入**：
  - 点击麦克风按钮开始语音识别
  - 说话时会显示"正在听取语音..."
  - 识别完成后自动发送消息
  
- **🔊 语音播放**：
  - 点击AI消息旁的喇叭按钮听取发音
  - 可在设置中开启自动朗读功能

## ⚙️ 设置功能

访问设置页面：点击导航栏的"设置"按钮

### 🎨 外观设置
- **主题模式**：
  - ☀️ 浅色主题
  - 🌙 深色主题
- **界面语言**：简体中文、繁体中文、English

### 🔊 语音设置
- **启用语音合成**：开启/关闭AI语音朗读
- **自动播放**：AI回复后自动朗读
- **语音选择**：选择不同的英语语音
- **语速调节**：0.5x - 2.0x（默认1.0x）
- **音调调节**：0.5 - 2.0（默认1.0）
- **音量调节**：0% - 100%（默认100%）

### 🎤 语音识别设置
- **启用语音识别**：开启/关闭麦克风输入
- **识别语言**：选择语音识别语言
  - English (US)
  - English (UK)
  - English (AU)
  - 等多种英语方言
- **连续识别**：持续监听语音输入
- **实时结果**：显示识别过程中的临时结果

### 🤖 AI设置 - 重点功能

#### 更换提示词（System Prompt）
这是最重要的自定义功能，可以改变AI的角色和行为：

**默认提示词**：
```
你是一个英语对话练习助手，请用英语与用户对话，帮助用户提高英语口语水平。
```

**常用提示词示例**：

1. **商务英语老师**：
```
You are a professional business English teacher. Help users practice business conversations, including meetings, presentations, negotiations, and professional emails. Provide corrections and suggestions for improvement.
```

2. **旅游英语助手**：
```
You are a travel English assistant. Help users practice English conversations they might need while traveling, such as booking hotels, ordering food, asking for directions, and shopping.
```

3. **面试英语教练**：
```
You are an English interview coach. Help users practice job interview conversations, including self-introduction, answering common interview questions, and asking appropriate questions to interviewers.
```

4. **日常口语伙伴**：
```
You are a friendly English conversation partner. Chat naturally about daily topics like hobbies, weather, food, movies, and current events. Keep the conversation engaging and help improve fluency.
```

5. **学术英语导师**：
```
You are an academic English tutor. Help users practice formal academic discussions, presentations, and debates. Focus on advanced vocabulary and complex sentence structures.
```

#### 其他AI设置
- **AI模型**：选择不同的AI模型（GPT-3.5、GPT-4、Claude-3）
- **创造性**：0.0-1.0，控制AI回复的创新程度
  - 0.0：更保守、一致的回复
  - 1.0：更有创意、多样的回复
- **最大回复长度**：100-2000字符，控制AI回复的长度

### 💾 数据管理
- **导出数据**：导出所有对话历史和设置为JSON文件
- **导入数据**：从JSON文件导入之前的数据
- **清除所有数据**：删除所有对话记录（谨慎操作）

## 📚 对话历史

### 查看历史
- 点击导航栏的"历史"查看所有对话
- 显示对话统计：总对话数、总消息数、平均消息数

### 管理对话
- **搜索对话**：🔍 按关键词搜索对话内容
- **编辑标题**：✏️ 修改对话标题
- **删除对话**：🗑️ 删除单个对话
- **清空所有**：删除所有对话历史

### 继续对话
- 点击任意历史对话可以继续该对话
- AI会记住之前的所有对话内容

## 🎯 使用技巧

### 提高学习效果
1. **设置合适的提示词**：根据学习目标选择对应的AI角色
2. **使用语音功能**：练习发音和听力
3. **保持对话连续性**：在同一对话中深入讨论话题
4. **定期复习**：查看对话历史，复习学过的内容

### 常见问题解决
1. **语音识别不准确**：
   - 检查麦克风权限
   - 调整语音识别语言设置
   - 在安静环境中使用

2. **AI回复太长/太短**：
   - 调整"最大回复长度"设置
   - 在提示词中指定回复长度要求

3. **AI回复不够自然**：
   - 降低"创造性"设置
   - 使用更具体的提示词

## 🔧 技术要求

### 系统要求
- Node.js 14+
- MongoDB 4.4+
- 现代浏览器（Chrome、Firefox、Safari、Edge）

### 网络要求
- 稳定的互联网连接（用于AI服务）
- 麦克风权限（用于语音输入）
- 扬声器/耳机（用于语音播放）

## 📞 技术支持

如遇问题，请检查：
1. 后端服务是否正常运行
2. 数据库连接是否正常
3. 浏览器控制台是否有错误信息
4. 网络连接是否稳定
