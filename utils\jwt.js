const jwt = require('jsonwebtoken');
const config = require('../config');

class JWTService {
  // 生成访问令牌
  generateAccessToken(payload) {
    return jwt.sign(
      payload,
      config.jwt.secret,
      {
        expiresIn: config.jwt.expiresIn,
        issuer: 'englishlearning-api',
        audience: 'englishlearning-client'
      }
    );
  }

  // 生成刷新令牌
  generateRefreshToken(payload) {
    return jwt.sign(
      payload,
      config.jwt.secret,
      {
        expiresIn: '30d', // 刷新令牌有效期30天
        issuer: 'englishlearning-api',
        audience: 'englishlearning-client'
      }
    );
  }

  // 验证令牌
  verifyToken(token) {
    try {
      return jwt.verify(token, config.jwt.secret, {
        issuer: 'englishlearning-api',
        audience: 'englishlearning-client'
      });
    } catch (error) {
      throw new Error(`令牌验证失败: ${error.message}`);
    }
  }

  // 解码令牌（不验证）
  decodeToken(token) {
    return jwt.decode(token);
  }

  // 生成用户令牌对
  generateTokenPair(user) {
    const payload = {
      userId: user._id,
      username: user.username,
      email: user.email,
      role: user.role || 'user'
    };

    const accessToken = this.generateAccessToken(payload);
    const refreshToken = this.generateRefreshToken({ userId: user._id });

    return {
      accessToken,
      refreshToken,
      expiresIn: config.jwt.expiresIn
    };
  }

  // 从请求头中提取令牌
  extractTokenFromHeader(authHeader) {
    if (!authHeader) {
      throw new Error('缺少Authorization头');
    }

    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      throw new Error('Authorization头格式错误');
    }

    return parts[1];
  }

  // 检查令牌是否即将过期
  isTokenExpiringSoon(token, thresholdMinutes = 15) {
    try {
      const decoded = this.decodeToken(token);
      const now = Math.floor(Date.now() / 1000);
      const threshold = thresholdMinutes * 60;
      
      return decoded.exp - now < threshold;
    } catch (error) {
      return true; // 如果无法解码，认为需要刷新
    }
  }
}

module.exports = new JWTService();
