<template>
  <div class="history-view">
    <div class="history-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>对话历史</h1>
        <div class="header-actions">
          <button 
            @click="showSearchDialog = true"
            class="action-btn"
            title="搜索对话"
          >
            🔍
          </button>
          <button 
            @click="showDeleteAllDialog = true"
            class="action-btn danger"
            :disabled="conversations.length === 0"
            title="清空所有对话"
          >
            🗑️
          </button>
        </div>
      </div>
      
      <!-- 统计信息 -->
      <div class="stats-section">
        <div class="stat-card">
          <div class="stat-number">{{ conversations.length }}</div>
          <div class="stat-label">总对话数</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ totalMessages }}</div>
          <div class="stat-label">总消息数</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ averageMessagesPerConversation }}</div>
          <div class="stat-label">平均消息数</div>
        </div>
      </div>
      
      <!-- 对话列表 -->
      <div class="conversations-section">
        <div v-if="filteredConversations.length === 0" class="empty-state">
          <div class="empty-icon">📚</div>
          <h3>{{ conversations.length === 0 ? '还没有对话历史' : '没有找到匹配的对话' }}</h3>
          <p>{{ conversations.length === 0 ? '开始你的第一次英语对话吧！' : '尝试使用不同的搜索关键词' }}</p>
          <router-link v-if="conversations.length === 0" to="/" class="start-chat-btn">
            开始对话
          </router-link>
        </div>
        
        <div v-else class="conversation-list">
          <div 
            v-for="conversation in paginatedConversations" 
            :key="conversation.id"
            class="conversation-card"
            @click="openConversation(conversation)"
          >
            <div class="conversation-header">
              <h3 class="conversation-title">{{ conversation.title }}</h3>
              <div class="conversation-actions">
                <button 
                  @click.stop="editTitle(conversation)"
                  class="action-btn small"
                  title="编辑标题"
                >
                  ✏️
                </button>
                <button 
                  @click.stop="deleteConversation(conversation.id)"
                  class="action-btn small danger"
                  title="删除对话"
                >
                  🗑️
                </button>
              </div>
            </div>
            
            <div class="conversation-meta">
              <span class="message-count">{{ conversation.messages.length }} 条消息</span>
              <span class="conversation-date">{{ formatDate(conversation.updatedAt) }}</span>
            </div>
            
            <div v-if="conversation.messages.length > 0" class="conversation-preview">
              <div class="preview-message">
                <span class="message-type">{{ getLastMessage(conversation).type === 'user' ? '你' : 'AI' }}:</span>
                <span class="message-content">{{ truncateText(getLastMessage(conversation).content, 100) }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 分页 -->
        <div v-if="totalPages > 1" class="pagination">
          <button 
            @click="currentPage = Math.max(1, currentPage - 1)"
            :disabled="currentPage === 1"
            class="page-btn"
          >
            ← 上一页
          </button>
          
          <div class="page-info">
            第 {{ currentPage }} 页，共 {{ totalPages }} 页
          </div>
          
          <button 
            @click="currentPage = Math.min(totalPages, currentPage + 1)"
            :disabled="currentPage === totalPages"
            class="page-btn"
          >
            下一页 →
          </button>
        </div>
      </div>
    </div>
    
    <!-- 搜索对话框 -->
    <div v-if="showSearchDialog" class="dialog-overlay" @click="closeSearchDialog">
      <div class="dialog" @click.stop>
        <div class="dialog-header">
          <h3>搜索对话</h3>
          <button @click="closeSearchDialog" class="close-btn">✕</button>
        </div>
        <div class="dialog-body">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="输入关键词搜索对话内容..."
            class="search-input"
            @keyup.enter="closeSearchDialog"
            ref="searchInput"
          >
        </div>
        <div class="dialog-footer">
          <button @click="clearSearch" class="btn secondary">清除</button>
          <button @click="closeSearchDialog" class="btn primary">确定</button>
        </div>
      </div>
    </div>
    
    <!-- 删除确认对话框 -->
    <div v-if="showDeleteAllDialog" class="dialog-overlay" @click="showDeleteAllDialog = false">
      <div class="dialog" @click.stop>
        <div class="dialog-header">
          <h3>确认删除</h3>
        </div>
        <div class="dialog-body">
          <p>确定要删除所有对话历史吗？此操作无法撤销。</p>
        </div>
        <div class="dialog-footer">
          <button @click="showDeleteAllDialog = false" class="btn secondary">取消</button>
          <button @click="deleteAllConversations" class="btn danger">删除所有</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, nextTick, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useChatStore } from '../stores/chat'

export default {
  name: 'HistoryView',
  setup() {
    const router = useRouter()
    const chatStore = useChatStore()
    
    const searchQuery = ref('')
    const showSearchDialog = ref(false)
    const showDeleteAllDialog = ref(false)
    const searchInput = ref(null)
    const currentPage = ref(1)
    const pageSize = 10
    
    // 计算属性
    const conversations = computed(() => chatStore.conversations)
    
    const totalMessages = computed(() => {
      return conversations.value.reduce((total, conv) => total + conv.messages.length, 0)
    })
    
    const averageMessagesPerConversation = computed(() => {
      if (conversations.value.length === 0) return 0
      return Math.round(totalMessages.value / conversations.value.length)
    })
    
    const filteredConversations = computed(() => {
      if (!searchQuery.value.trim()) {
        return conversations.value
      }
      
      const query = searchQuery.value.toLowerCase()
      return conversations.value.filter(conversation => {
        // 搜索标题
        if (conversation.title.toLowerCase().includes(query)) {
          return true
        }
        
        // 搜索消息内容
        return conversation.messages.some(message => 
          message.content.toLowerCase().includes(query)
        )
      })
    })
    
    const totalPages = computed(() => {
      return Math.ceil(filteredConversations.value.length / pageSize)
    })
    
    const paginatedConversations = computed(() => {
      const start = (currentPage.value - 1) * pageSize
      const end = start + pageSize
      return filteredConversations.value.slice(start, end)
    })
    
    // 方法
    const openConversation = (conversation) => {
      chatStore.switchConversation(conversation.id)
      router.push('/')
    }
    
    const deleteConversation = (conversationId) => {
      if (confirm('确定要删除这个对话吗？')) {
        chatStore.deleteConversation(conversationId)
        
        // 调整当前页码
        if (paginatedConversations.value.length === 0 && currentPage.value > 1) {
          currentPage.value--
        }
      }
    }
    
    const deleteAllConversations = () => {
      conversations.value.forEach(conv => {
        chatStore.deleteConversation(conv.id)
      })
      showDeleteAllDialog.value = false
      currentPage.value = 1
    }
    
    const editTitle = (conversation) => {
      const newTitle = prompt('请输入新的对话标题:', conversation.title)
      if (newTitle && newTitle.trim() !== conversation.title) {
        conversation.title = newTitle.trim()
      }
    }
    
    const getLastMessage = (conversation) => {
      return conversation.messages[conversation.messages.length - 1]
    }
    
    const formatDate = (date) => {
      const now = new Date()
      const messageDate = new Date(date)
      const diffTime = now - messageDate
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))
      
      if (diffDays === 0) {
        return messageDate.toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit'
        })
      } else if (diffDays === 1) {
        return '昨天'
      } else if (diffDays < 7) {
        return `${diffDays}天前`
      } else {
        return messageDate.toLocaleDateString('zh-CN')
      }
    }
    
    const truncateText = (text, maxLength) => {
      if (text.length <= maxLength) return text
      return text.substring(0, maxLength) + '...'
    }
    
    const closeSearchDialog = () => {
      showSearchDialog.value = false
    }
    
    const clearSearch = () => {
      searchQuery.value = ''
      currentPage.value = 1
    }
    
    // 监听搜索对话框打开
    const openSearchDialog = async () => {
      showSearchDialog.value = true
      await nextTick()
      if (searchInput.value) {
        searchInput.value.focus()
      }
    }
    
    onMounted(() => {
      // 重置页码
      currentPage.value = 1
    })
    
    return {
      conversations,
      totalMessages,
      averageMessagesPerConversation,
      filteredConversations,
      paginatedConversations,
      totalPages,
      currentPage,
      searchQuery,
      showSearchDialog,
      showDeleteAllDialog,
      searchInput,
      openConversation,
      deleteConversation,
      deleteAllConversations,
      editTitle,
      getLastMessage,
      formatDate,
      truncateText,
      closeSearchDialog,
      clearSearch,
      openSearchDialog
    }
  }
}
</script>

<style scoped>
.history-view {
  min-height: calc(100vh - 64px);
  background-color: var(--background-color);
}

.history-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.page-header h1 {
  color: var(--text-primary);
  font-size: 2rem;
  font-weight: 700;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  padding: 0.5rem;
  cursor: pointer;
  font-size: 1.125rem;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background-color: var(--background-color);
}

.action-btn.danger {
  color: var(--error-color);
}

.action-btn.danger:hover {
  background-color: var(--error-color);
  color: white;
}

.action-btn.small {
  padding: 0.25rem;
  font-size: 0.875rem;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  padding: 1.5rem;
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.stat-label {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.conversations-section {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  padding: 1.5rem;
}

.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--text-secondary);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.empty-state h3 {
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.start-chat-btn {
  display: inline-block;
  margin-top: 1rem;
  padding: 0.75rem 1.5rem;
  background-color: var(--primary-color);
  color: white;
  text-decoration: none;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.start-chat-btn:hover {
  background-color: var(--primary-hover);
}

.conversation-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.conversation-card {
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.conversation-card:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow);
}

.conversation-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.conversation-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  flex: 1;
}

.conversation-actions {
  display: flex;
  gap: 0.25rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.conversation-card:hover .conversation-actions {
  opacity: 1;
}

.conversation-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.conversation-preview {
  border-top: 1px solid var(--border-color);
  padding-top: 0.75rem;
}

.preview-message {
  font-size: 0.875rem;
  line-height: 1.4;
}

.message-type {
  font-weight: 600;
  color: var(--text-primary);
}

.message-content {
  color: var(--text-secondary);
}

.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.page-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.page-btn:hover:not(:disabled) {
  background-color: var(--primary-hover);
}

.page-btn:disabled {
  background-color: var(--border-color);
  cursor: not-allowed;
}

.page-info {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* 对话框样式 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dialog {
  background: var(--surface-color);
  border-radius: 0.75rem;
  box-shadow: var(--shadow-lg);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.dialog-header h3 {
  margin: 0;
  color: var(--text-primary);
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  color: var(--text-secondary);
  padding: 0.25rem;
}

.dialog-body {
  padding: 1.5rem;
}

.search-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  font-size: 1rem;
  background-color: var(--background-color);
  color: var(--text-primary);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1.5rem;
  border-top: 1px solid var(--border-color);
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn.primary {
  background-color: var(--primary-color);
  color: white;
}

.btn.primary:hover {
  background-color: var(--primary-hover);
}

.btn.secondary {
  background-color: var(--background-color);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn.secondary:hover {
  background-color: var(--border-color);
}

.btn.danger {
  background-color: var(--error-color);
  color: white;
}

.btn.danger:hover {
  background-color: #dc2626;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .history-container {
    padding: 1rem 0.5rem;
  }
  
  .page-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .page-header h1 {
    font-size: 1.5rem;
    text-align: center;
  }
  
  .stats-section {
    grid-template-columns: 1fr;
  }
  
  .conversation-header {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .conversation-actions {
    opacity: 1;
    align-self: flex-end;
  }
  
  .conversation-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .pagination {
    flex-direction: column;
    gap: 1rem;
  }
}
</style>
