const express = require('express');
const router = express.Router();
const authController = require('../controllers/authController');
const { authenticateToken, rateLimit } = require('../middleware/auth');

// 应用速率限制
const authRateLimit = rateLimit(15 * 60 * 1000, 10); // 15分钟内最多10次请求
const loginRateLimit = rateLimit(15 * 60 * 1000, 5);  // 15分钟内最多5次登录尝试

// 用户注册
router.post('/register', authRateLimit, authController.register);

// 用户登录
router.post('/login', loginRateLimit, authController.login);

// 刷新令牌
router.post('/refresh', authRateLimit, authController.refreshToken);

// 获取当前用户信息（需要认证）
router.get('/me', authenticateToken, authController.getCurrentUser);

// 更新用户设置（需要认证）
router.put('/settings', authenticateToken, authController.updateSettings);

// 修改密码（需要认证）
router.put('/password', authenticateToken, authController.changePassword);

// 登出（需要认证）
router.post('/logout', authenticateToken, authController.logout);

// 验证令牌有效性
router.get('/verify', authenticateToken, (req, res) => {
  res.json({
    valid: true,
    user: {
      id: req.user._id,
      username: req.user.username,
      email: req.user.email
    }
  });
});

module.exports = router;
