const User = require('../models/User');
const jwtService = require('../utils/jwt');
const crypto = require('crypto');

class AuthController {
  // 用户注册
  async register(req, res) {
    try {
      const { username, email, password, firstName, lastName } = req.body;

      // 验证必需字段
      if (!username || !email || !password) {
        return res.status(400).json({
          error: '参数错误',
          message: '用户名、邮箱和密码都是必需的'
        });
      }

      // 检查用户名和邮箱是否已存在
      const existingUser = await User.findOne({
        $or: [{ email }, { username }]
      });

      if (existingUser) {
        const field = existingUser.email === email ? '邮箱' : '用户名';
        return res.status(409).json({
          error: '用户已存在',
          message: `该${field}已被注册`
        });
      }

      // 创建新用户
      const userData = {
        username,
        email,
        password,
        profile: {
          firstName: firstName || '',
          lastName: lastName || ''
        },
        emailVerificationToken: crypto.randomBytes(32).toString('hex')
      };

      const user = new User(userData);
      await user.save();

      // 生成令牌
      const tokens = jwtService.generateTokenPair(user);

      // 返回用户信息（不包含密码）
      const userResponse = {
        id: user._id,
        username: user.username,
        email: user.email,
        profile: user.profile,
        settings: user.settings,
        isEmailVerified: user.isEmailVerified,
        createdAt: user.createdAt
      };

      res.status(201).json({
        message: '注册成功',
        user: userResponse,
        tokens
      });

    } catch (error) {
      console.error('注册错误:', error);
      
      if (error.name === 'ValidationError') {
        const errors = Object.values(error.errors).map(err => err.message);
        return res.status(400).json({
          error: '数据验证失败',
          message: errors.join(', ')
        });
      }

      res.status(500).json({
        error: '注册失败',
        message: '服务器内部错误'
      });
    }
  }

  // 用户登录
  async login(req, res) {
    try {
      const { email, password, rememberMe = false } = req.body;

      // 验证必需字段
      if (!email || !password) {
        return res.status(400).json({
          error: '参数错误',
          message: '邮箱和密码都是必需的'
        });
      }

      // 查找用户（包含密码字段）
      const user = await User.findOne({ email }).select('+password');
      
      if (!user) {
        return res.status(401).json({
          error: '登录失败',
          message: '邮箱或密码错误'
        });
      }

      // 验证密码
      const isPasswordValid = await user.comparePassword(password);
      
      if (!isPasswordValid) {
        return res.status(401).json({
          error: '登录失败',
          message: '邮箱或密码错误'
        });
      }

      // 检查账户状态
      if (!user.isActive) {
        return res.status(401).json({
          error: '账户已禁用',
          message: '您的账户已被禁用，请联系管理员'
        });
      }

      // 更新最后登录时间
      user.lastLoginAt = new Date();
      await user.save();

      // 生成令牌
      const tokens = jwtService.generateTokenPair(user);

      // 返回用户信息
      const userResponse = {
        id: user._id,
        username: user.username,
        email: user.email,
        profile: user.profile,
        settings: user.settings,
        learningStats: user.learningStats,
        isEmailVerified: user.isEmailVerified,
        lastLoginAt: user.lastLoginAt
      };

      res.json({
        message: '登录成功',
        user: userResponse,
        tokens
      });

    } catch (error) {
      console.error('登录错误:', error);
      res.status(500).json({
        error: '登录失败',
        message: '服务器内部错误'
      });
    }
  }

  // 刷新令牌
  async refreshToken(req, res) {
    try {
      const { refreshToken } = req.body;

      if (!refreshToken) {
        return res.status(400).json({
          error: '参数错误',
          message: '缺少刷新令牌'
        });
      }

      // 验证刷新令牌
      const decoded = jwtService.verifyToken(refreshToken);
      
      // 查找用户
      const user = await User.findById(decoded.userId);
      
      if (!user || !user.isActive) {
        return res.status(401).json({
          error: '令牌无效',
          message: '用户不存在或已被禁用'
        });
      }

      // 生成新的令牌对
      const tokens = jwtService.generateTokenPair(user);

      res.json({
        message: '令牌刷新成功',
        tokens
      });

    } catch (error) {
      console.error('刷新令牌错误:', error);
      res.status(401).json({
        error: '令牌刷新失败',
        message: '无效的刷新令牌'
      });
    }
  }

  // 获取当前用户信息
  async getCurrentUser(req, res) {
    try {
      const user = req.user;

      const userResponse = {
        id: user._id,
        username: user.username,
        email: user.email,
        profile: user.profile,
        settings: user.settings,
        learningStats: user.learningStats,
        isEmailVerified: user.isEmailVerified,
        lastLoginAt: user.lastLoginAt,
        createdAt: user.createdAt
      };

      res.json({
        user: userResponse
      });

    } catch (error) {
      console.error('获取用户信息错误:', error);
      res.status(500).json({
        error: '获取用户信息失败',
        message: '服务器内部错误'
      });
    }
  }

  // 更新用户设置
  async updateSettings(req, res) {
    try {
      const user = req.user;
      const { settings } = req.body;

      if (!settings) {
        return res.status(400).json({
          error: '参数错误',
          message: '缺少设置数据'
        });
      }

      // 更新设置
      user.settings = { ...user.settings, ...settings };
      await user.save();

      res.json({
        message: '设置更新成功',
        settings: user.settings
      });

    } catch (error) {
      console.error('更新设置错误:', error);
      res.status(500).json({
        error: '更新设置失败',
        message: '服务器内部错误'
      });
    }
  }

  // 修改密码
  async changePassword(req, res) {
    try {
      const user = req.user;
      const { currentPassword, newPassword } = req.body;

      if (!currentPassword || !newPassword) {
        return res.status(400).json({
          error: '参数错误',
          message: '当前密码和新密码都是必需的'
        });
      }

      // 获取包含密码的用户信息
      const userWithPassword = await User.findById(user._id).select('+password');
      
      // 验证当前密码
      const isCurrentPasswordValid = await userWithPassword.comparePassword(currentPassword);
      
      if (!isCurrentPasswordValid) {
        return res.status(400).json({
          error: '密码错误',
          message: '当前密码不正确'
        });
      }

      // 更新密码
      userWithPassword.password = newPassword;
      await userWithPassword.save();

      res.json({
        message: '密码修改成功'
      });

    } catch (error) {
      console.error('修改密码错误:', error);
      res.status(500).json({
        error: '修改密码失败',
        message: '服务器内部错误'
      });
    }
  }

  // 登出（客户端处理，服务端记录）
  async logout(req, res) {
    try {
      // 这里可以添加令牌黑名单逻辑
      // 目前只是返回成功响应
      res.json({
        message: '登出成功'
      });

    } catch (error) {
      console.error('登出错误:', error);
      res.status(500).json({
        error: '登出失败',
        message: '服务器内部错误'
      });
    }
  }
}

module.exports = new AuthController();
