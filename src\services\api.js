import axios from 'axios'

// API基础配置
const API_BASE_URL = 'http://localhost:3000/api'

// 创建axios实例
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 增加到30秒
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器 - 添加认证token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('accessToken')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 处理token过期
api.interceptors.response.use(
  (response) => {
    return response
  },
  async (error) => {
    const originalRequest = error.config

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      try {
        const refreshToken = localStorage.getItem('refreshToken')
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {
            refreshToken
          })
          
          const { accessToken } = response.data.tokens
          localStorage.setItem('accessToken', accessToken)
          
          // 重试原请求
          originalRequest.headers.Authorization = `Bearer ${accessToken}`
          return api(originalRequest)
        }
      } catch (refreshError) {
        // 刷新token失败，清除本地存储并跳转到登录页
        localStorage.removeItem('accessToken')
        localStorage.removeItem('refreshToken')
        localStorage.removeItem('user')
        window.location.href = '/login'
      }
    }

    return Promise.reject(error)
  }
)

// 认证API
export const authAPI = {
  // 用户注册
  register: (userData) => api.post('/auth/register', userData),
  
  // 用户登录
  login: (credentials) => api.post('/auth/login', credentials),
  
  // 刷新token
  refreshToken: (refreshToken) => api.post('/auth/refresh', { refreshToken }),
  
  // 获取当前用户信息
  getCurrentUser: () => api.get('/auth/me'),
  
  // 更新用户设置
  updateSettings: (settings) => api.put('/auth/settings', { settings }),
  
  // 修改密码
  changePassword: (passwordData) => api.put('/auth/password', passwordData),
  
  // 登出
  logout: () => api.post('/auth/logout'),
  
  // 验证token
  verifyToken: () => api.get('/auth/verify')
}

// AI对话API
export const aiAPI = {
  // 发送消息进行AI对话
  chat: (messageData) => api.post('/ai/chat', messageData),
  
  // 获取对话列表
  getConversations: (params = {}) => api.get('/ai/conversations', { params }),
  
  // 获取单个对话详情
  getConversation: (conversationId) => api.get(`/ai/conversations/${conversationId}`),
  
  // 删除对话
  deleteConversation: (conversationId) => api.delete(`/ai/conversations/${conversationId}`),
  
  // 语法检查
  checkGrammar: (text, language = 'english') => api.post('/ai/grammar-check', { text, language }),
  
  // 作文批改
  reviewEssay: (essayData) => api.post('/ai/essay-review', essayData),
  
  // 获取AI服务状态
  getStatus: () => api.get('/ai/status')
}

// 通用API
export const commonAPI = {
  // 健康检查
  healthCheck: () => axios.get('http://localhost:3000/health')
}

// 错误处理工具
export const handleApiError = (error) => {
  console.error('API错误详情:', error)

  if (error.response) {
    // 服务器返回错误状态码
    const { status, data } = error.response
    console.log('服务器响应错误:', { status, data })
    return {
      status,
      message: data.message || data.error || '服务器错误',
      details: data.details || null
    }
  } else if (error.request) {
    // 请求发送但没有收到响应
    console.log('网络请求失败:', error.request)
    console.log('请求配置:', error.config)
    return {
      status: 0,
      message: `网络连接失败，无法连接到 ${API_BASE_URL}`,
      details: {
        url: error.config?.url,
        method: error.config?.method,
        baseURL: error.config?.baseURL
      }
    }
  } else {
    // 其他错误
    console.log('其他错误:', error.message)
    return {
      status: -1,
      message: error.message || '未知错误',
      details: null
    }
  }
}

// 导出默认实例
export default api
