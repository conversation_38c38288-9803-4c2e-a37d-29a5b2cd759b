# 英语对话练习 - 前端应用

一个基于 Vue 3 的英语对话练习应用，集成了 AI 对话、语音识别和语音合成功能，帮助用户提高英语口语水平。

## ✨ 功能特性

### 🗣️ 智能对话
- AI 驱动的英语对话练习
- 自然语言处理和回复
- 多种对话场景和主题

### 🎤 语音功能
- **语音识别**: 支持多种语言的语音输入
- **语音合成**: AI 回复自动朗读
- **语音设置**: 可调节语速、音调、音量
- **多语音选择**: 支持多种英语语音

### 📚 对话管理
- 对话历史记录和管理
- 对话搜索和筛选
- 数据导入导出功能
- 本地数据持久化

### 🎨 用户体验
- 响应式设计，支持移动端
- 深色/浅色主题切换
- 现代化 UI 设计
- 流畅的动画效果

## 🛠️ 技术栈

- **前端框架**: Vue 3 (Composition API)
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **语音功能**: Web Speech API
- **样式**: CSS3 + 自定义 CSS 变量
- **构建工具**: Vue CLI

## 📦 项目结构

```
src/
├── assets/           # 静态资源
│   └── styles/      # 全局样式
├── components/      # 可复用组件
│   ├── NavBar.vue   # 导航栏
│   ├── LoadingSpinner.vue  # 加载动画
│   └── ErrorBoundary.vue   # 错误边界
├── composables/     # 组合式函数
│   ├── useSpeechRecognition.js  # 语音识别
│   └── useSpeechSynthesis.js    # 语音合成
├── stores/          # 状态管理
│   ├── chat.js      # 聊天状态
│   └── settings.js  # 设置状态
├── views/           # 页面组件
│   ├── ChatView.vue     # 聊天页面
│   ├── HistoryView.vue  # 历史页面
│   └── SettingsView.vue # 设置页面
├── router/          # 路由配置
└── main.js          # 应用入口
```

## 🚀 快速开始

### 环境要求
- Node.js 14+
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 开发环境运行
```bash
npm run serve
```

### 生产环境构建
```bash
npm run build
```

### 代码检查
```bash
npm run lint
```

## 🔧 配置说明

### 浏览器兼容性
- Chrome 25+
- Firefox 44+
- Safari 14.1+
- Edge 79+

### 语音功能要求
- 需要 HTTPS 环境（本地开发除外）
- 需要麦克风权限（语音识别）
- 需要音频播放权限（语音合成）

## 📱 功能使用

### 语音对话
1. 点击麦克风按钮开始语音输入
2. 说出英语内容
3. AI 会自动回复并朗读

### 设置配置
1. 进入设置页面
2. 调整语音参数（语速、音调、音量）
3. 选择合适的语音
4. 配置 AI 参数

### 历史管理
1. 查看所有对话历史
2. 搜索特定对话内容
3. 导出/导入数据
4. 删除不需要的对话

## 🎯 开发计划

- [ ] 集成真实的 AI API（OpenAI、Claude 等）
- [ ] 添加更多语音识别语言
- [ ] 实现语音评分功能
- [ ] 添加学习进度统计
- [ ] 支持多用户系统
- [ ] 添加离线模式

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- Vue.js 团队提供的优秀框架
- Web Speech API 的浏览器支持
- 所有开源贡献者的努力

---

**注意**: 这是前端应用，需要配合后端 API 才能实现完整的 AI 对话功能。
