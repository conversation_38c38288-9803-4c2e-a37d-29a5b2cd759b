import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authAPI, handleApiError } from '../services/api'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref(null)
  const accessToken = ref(null)
  const refreshToken = ref(null)
  const isLoading = ref(false)
  const error = ref(null)

  // 计算属性
  const isAuthenticated = computed(() => !!accessToken.value && !!user.value)
  const userDisplayName = computed(() => {
    if (!user.value) return ''
    return user.value.profile?.firstName 
      ? `${user.value.profile.firstName} ${user.value.profile.lastName || ''}`.trim()
      : user.value.username
  })

  // 初始化 - 从localStorage恢复状态
  const initializeAuth = () => {
    try {
      const savedToken = localStorage.getItem('accessToken')
      const savedRefreshToken = localStorage.getItem('refreshToken')
      const savedUser = localStorage.getItem('user')

      if (savedToken && savedUser) {
        accessToken.value = savedToken
        refreshToken.value = savedRefreshToken
        user.value = JSON.parse(savedUser)
      }
    } catch (error) {
      console.error('初始化认证状态失败:', error)
      clearAuth()
    }
  }

  // 清除认证状态
  const clearAuth = () => {
    user.value = null
    accessToken.value = null
    refreshToken.value = null
    localStorage.removeItem('accessToken')
    localStorage.removeItem('refreshToken')
    localStorage.removeItem('user')
  }

  // 保存认证状态
  const saveAuth = (authData) => {
    const { user: userData, tokens } = authData
    
    user.value = userData
    accessToken.value = tokens.accessToken
    refreshToken.value = tokens.refreshToken

    localStorage.setItem('accessToken', tokens.accessToken)
    localStorage.setItem('refreshToken', tokens.refreshToken)
    localStorage.setItem('user', JSON.stringify(userData))
  }

  // 用户注册
  const register = async (userData) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await authAPI.register(userData)
      saveAuth(response.data)
      return { success: true, data: response.data }
    } catch (err) {
      const errorInfo = handleApiError(err)
      error.value = errorInfo.message
      return { success: false, error: errorInfo }
    } finally {
      isLoading.value = false
    }
  }

  // 用户登录
  const login = async (credentials) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await authAPI.login(credentials)
      saveAuth(response.data)
      return { success: true, data: response.data }
    } catch (err) {
      const errorInfo = handleApiError(err)
      error.value = errorInfo.message
      return { success: false, error: errorInfo }
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = async () => {
    isLoading.value = true

    try {
      if (accessToken.value) {
        await authAPI.logout()
      }
    } catch (err) {
      console.error('登出请求失败:', err)
    } finally {
      clearAuth()
      isLoading.value = false
    }
  }

  // 获取当前用户信息
  const fetchCurrentUser = async () => {
    if (!accessToken.value) return { success: false, error: '未登录' }

    isLoading.value = true
    error.value = null

    try {
      const response = await authAPI.getCurrentUser()
      user.value = response.data.user
      localStorage.setItem('user', JSON.stringify(response.data.user))
      return { success: true, data: response.data.user }
    } catch (err) {
      const errorInfo = handleApiError(err)
      error.value = errorInfo.message
      
      // 如果是认证错误，清除本地状态
      if (errorInfo.status === 401) {
        clearAuth()
      }
      
      return { success: false, error: errorInfo }
    } finally {
      isLoading.value = false
    }
  }

  // 更新用户设置
  const updateSettings = async (settings) => {
    if (!accessToken.value) return { success: false, error: '未登录' }

    isLoading.value = true
    error.value = null

    try {
      const response = await authAPI.updateSettings(settings)
      
      // 更新本地用户信息
      if (user.value) {
        user.value.settings = { ...user.value.settings, ...settings }
        localStorage.setItem('user', JSON.stringify(user.value))
      }
      
      return { success: true, data: response.data }
    } catch (err) {
      const errorInfo = handleApiError(err)
      error.value = errorInfo.message
      return { success: false, error: errorInfo }
    } finally {
      isLoading.value = false
    }
  }

  // 修改密码
  const changePassword = async (passwordData) => {
    if (!accessToken.value) return { success: false, error: '未登录' }

    isLoading.value = true
    error.value = null

    try {
      const response = await authAPI.changePassword(passwordData)
      return { success: true, data: response.data }
    } catch (err) {
      const errorInfo = handleApiError(err)
      error.value = errorInfo.message
      return { success: false, error: errorInfo }
    } finally {
      isLoading.value = false
    }
  }

  // 验证token有效性
  const verifyToken = async () => {
    if (!accessToken.value) return false

    try {
      await authAPI.verifyToken()
      return true
    } catch (err) {
      clearAuth()
      return false
    }
  }

  // 清除错误
  const clearError = () => {
    error.value = null
  }

  return {
    // 状态
    user,
    accessToken,
    refreshToken,
    isLoading,
    error,
    
    // 计算属性
    isAuthenticated,
    userDisplayName,
    
    // 方法
    initializeAuth,
    clearAuth,
    register,
    login,
    logout,
    fetchCurrentUser,
    updateSettings,
    changePassword,
    verifyToken,
    clearError
  }
})
