const jwtService = require('../utils/jwt');
const User = require('../models/User');

// 验证JWT令牌中间件
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      return res.status(401).json({
        error: '未授权访问',
        message: '请提供有效的访问令牌'
      });
    }

    const token = jwtService.extractTokenFromHeader(authHeader);
    const decoded = jwtService.verifyToken(token);

    // 从数据库获取用户信息
    const user = await User.findById(decoded.userId).select('-password');
    
    if (!user) {
      return res.status(401).json({
        error: '用户不存在',
        message: '令牌中的用户不存在'
      });
    }

    if (!user.isActive) {
      return res.status(401).json({
        error: '账户已禁用',
        message: '您的账户已被禁用，请联系管理员'
      });
    }

    // 将用户信息添加到请求对象
    req.user = user;
    req.token = token;
    
    next();
  } catch (error) {
    console.error('认证中间件错误:', error);
    
    let statusCode = 401;
    let message = '令牌验证失败';
    
    if (error.name === 'TokenExpiredError') {
      message = '令牌已过期，请重新登录';
    } else if (error.name === 'JsonWebTokenError') {
      message = '无效的令牌格式';
    } else if (error.name === 'NotBeforeError') {
      message = '令牌尚未生效';
    }
    
    return res.status(statusCode).json({
      error: '认证失败',
      message
    });
  }
};

// 可选认证中间件（不强制要求登录）
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      req.user = null;
      return next();
    }

    const token = jwtService.extractTokenFromHeader(authHeader);
    const decoded = jwtService.verifyToken(token);
    const user = await User.findById(decoded.userId).select('-password');
    
    if (user && user.isActive) {
      req.user = user;
      req.token = token;
    } else {
      req.user = null;
    }
    
    next();
  } catch (error) {
    // 可选认证失败时不返回错误，只是不设置用户信息
    req.user = null;
    next();
  }
};

// 角色验证中间件
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: '未授权访问',
        message: '请先登录'
      });
    }

    const userRole = req.user.role || 'user';
    const allowedRoles = Array.isArray(roles) ? roles : [roles];
    
    if (!allowedRoles.includes(userRole)) {
      return res.status(403).json({
        error: '权限不足',
        message: '您没有权限访问此资源'
      });
    }
    
    next();
  };
};

// 验证用户是否为资源所有者
const requireOwnership = (resourceUserIdField = 'userId') => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: '未授权访问',
        message: '请先登录'
      });
    }

    // 从请求参数、查询参数或请求体中获取资源的用户ID
    const resourceUserId = req.params[resourceUserIdField] || 
                          req.query[resourceUserIdField] || 
                          req.body[resourceUserIdField];

    if (!resourceUserId) {
      return res.status(400).json({
        error: '参数错误',
        message: '缺少资源用户ID'
      });
    }

    // 检查是否为资源所有者或管理员
    if (req.user._id.toString() !== resourceUserId.toString() && 
        req.user.role !== 'admin') {
      return res.status(403).json({
        error: '权限不足',
        message: '您只能访问自己的资源'
      });
    }
    
    next();
  };
};

// 限制访问频率中间件
const rateLimit = (windowMs = 15 * 60 * 1000, maxRequests = 100) => {
  const requests = new Map();
  
  return (req, res, next) => {
    const identifier = req.user ? req.user._id.toString() : req.ip;
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // 清理过期记录
    if (requests.has(identifier)) {
      const userRequests = requests.get(identifier);
      const validRequests = userRequests.filter(time => time > windowStart);
      requests.set(identifier, validRequests);
    }
    
    // 检查请求次数
    const userRequests = requests.get(identifier) || [];
    
    if (userRequests.length >= maxRequests) {
      return res.status(429).json({
        error: '请求过于频繁',
        message: `请在 ${Math.ceil(windowMs / 60000)} 分钟后重试`,
        retryAfter: Math.ceil(windowMs / 1000)
      });
    }
    
    // 记录当前请求
    userRequests.push(now);
    requests.set(identifier, userRequests);
    
    next();
  };
};

// 验证邮箱是否已验证
const requireEmailVerification = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      error: '未授权访问',
      message: '请先登录'
    });
  }

  if (!req.user.isEmailVerified) {
    return res.status(403).json({
      error: '邮箱未验证',
      message: '请先验证您的邮箱地址'
    });
  }
  
  next();
};

module.exports = {
  authenticateToken,
  optionalAuth,
  requireRole,
  requireOwnership,
  rateLimit,
  requireEmailVerification
};
