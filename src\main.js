import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import './assets/styles/global.css'

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)

// 挂载应用并初始化数据
app.mount('#app')

// 初始化数据存储
import { useChatStore } from './stores/chat'
import { useSettingsStore } from './stores/settings'

// 等待应用挂载后再初始化数据
setTimeout(() => {
  const chatStore = useChatStore()
  const settingsStore = useSettingsStore()

  // 加载保存的数据
  chatStore.loadFromStorage()
  settingsStore.loadSettings()

  // 启动自动保存
  chatStore.startAutoSave()
}, 0)
